#!/usr/bin/env python3
"""
调试文件变化 - 查看哪些文件在不断变化
"""
import time
import os
from pathlib import Path
from watchfiles import watch

def debug_file_changes():
    """调试文件变化"""
    print("🔍 开始监控文件变化...")
    print("按 Ctrl+C 停止监控\n")
    
    project_root = Path(".")
    
    try:
        for changes in watch(project_root):
            for change_type, file_path in changes:
                rel_path = Path(file_path).relative_to(project_root)
                print(f"📝 {change_type.name}: {rel_path}")
                
                # 如果是频繁变化的文件，给出建议
                if any(pattern in str(rel_path) for pattern in [
                    "__pycache__", ".pyc", ".log", "temp", "tmp", 
                    "node_modules", ".git", "uploads"
                ]):
                    print(f"   💡 建议: 将 {rel_path} 添加到忽略列表")
                
                print()
                
    except KeyboardInterrupt:
        print("\n✅ 监控已停止")

if __name__ == "__main__":
    debug_file_changes()
