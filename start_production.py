#!/usr/bin/env python3
"""
生产环境启动脚本 - 不使用文件监控
"""
import uvicorn
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

if __name__ == "__main__":
    print("🚀 启动生产环境服务器...")
    
    # 启动服务器（生产模式，无文件监控）
    uvicorn.run(
        "backend_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # 关闭文件监控
        log_level="info",
        workers=1
    )
