* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #0f0f0f;
  color: #ffffff;
}

#root {
  min-height: 100vh;
  background-color: #0f0f0f;
}

.ant-layout {
  min-height: 100vh;
  background-color: #0f0f0f !important;
}

.ant-layout-header {
  background-color: #1a1a1a !important;
  border-bottom: 1px solid #2d2d2d !important;
}

.ant-layout-content {
  background-color: #0f0f0f !important;
}

/* 自定义滚动条 - 深色主题 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* 合集横向滚动容器样式 */
.collections-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #404040 #1a1a1a;
}

.collections-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.collections-scroll-container::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.collections-scroll-container::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

.collections-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Ant Design 深色主题覆盖 */
.ant-card {
  background-color: #1a1a1a !important;
  border: 1px solid #2d2d2d !important;
  border-radius: 12px !important;
}

.ant-card-meta-title {
  color: #ffffff !important;
}

.ant-card-meta-description {
  color: #cccccc !important;
}

.ant-typography {
  color: #ffffff !important;
}

.ant-typography-caption {
  color: #999999 !important;
}

/* 输入框样式增强 */
.ant-input {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.ant-input:focus {
  border-color: #4facfe !important;
  box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2) !important;
}

.ant-input:hover {
  border-color: #555555 !important;
}

.ant-input::placeholder {
  color: #888888 !important;
}

/* 密码输入框 */
.ant-input-password {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.ant-input-password .ant-input-suffix {
  color: #888888 !important;
}

.ant-input-password .ant-input-suffix:hover {
  color: #4facfe !important;
}

/* 数字输入框 */
.ant-input-number {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.ant-input-number .ant-input-number-input {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
}

/* 选择器样式 */
.ant-select {
  color: #ffffff !important;
}

.ant-select-selector {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.ant-select-selector:hover {
  border-color: #555555 !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #4facfe !important;
  box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.2) !important;
}

.ant-select-dropdown {
  background-color: #1a1a1a !important;
  border: 1px solid #2d2d2d !important;
  border-radius: 8px !important;
}

.ant-select-item {
  color: #ffffff !important;
}

.ant-select-item:hover {
  background-color: #2d2d2d !important;
}

.ant-select-item-option-selected {
  background-color: #4facfe !important;
  color: #ffffff !important;
}

/* 按钮样式增强 */
.ant-btn {
  border-radius: 8px !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background-color: #4facfe !important;
  border-color: #4facfe !important;
  color: #ffffff !important;
}

.ant-btn-primary:hover {
  background-color: #00a8ff !important;
  border-color: #00a8ff !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.ant-btn-default {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.ant-btn-default:hover {
  background-color: #404040 !important;
  border-color: #555555 !important;
  color: #ffffff !important;
}

/* 表单样式 */
.ant-form-item-label > label {
  color: #ffffff !important;
  font-weight: 500;
}

.ant-form-item-explain-error {
  color: #ff6b6b !important;
  font-size: 12px;
}

.ant-form-item-explain-warning {
  color: #faad14 !important;
  font-size: 12px;
}

/* 警告框样式 */
.ant-alert {
  border-radius: 8px !important;
  border: 1px solid #2d2d2d !important;
}

.ant-alert-info {
  background-color: #1a1a1a !important;
  border-color: #2d2d2d !important;
}

.ant-alert-info .ant-alert-message {
  color: #ffffff !important;
}

.ant-alert-info .ant-alert-description {
  color: #cccccc !important;
}

.ant-alert-info .ant-alert-icon {
  color: #4facfe !important;
}

/* 分割线样式 */
.ant-divider {
  border-color: #2d2d2d !important;
}

.ant-divider-horizontal {
  margin: 16px 0 !important;
}

.ant-divider-inner-text {
  color: #888888 !important;
  font-weight: 500;
}

/* 空状态样式 */
.ant-empty {
  color: #999999 !important;
}

.ant-empty-description {
  color: #999999 !important;
}

/* 加载状态 */
.ant-spin-dot-item {
  background-color: #4facfe !important;
}

.ant-spin-text {
  color: #4facfe !important;
}

/* 消息提示样式 */
.ant-message-notice-content {
  background-color: #1a1a1a !important;
  border: 1px solid #2d2d2d !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.ant-message-success .anticon {
  color: #52c41a !important;
}

.ant-message-error .anticon {
  color: #ff6b6b !important;
}

.ant-message-warning .anticon {
  color: #faad14 !important;
}

.ant-message-info .anticon {
  color: #4facfe !important;
}

/* 模态框样式 */
.ant-modal-content {
  background-color: #1a1a1a !important;
  border-radius: 12px !important;
  border: 1px solid #2d2d2d !important;
}

.ant-modal-header {
  background-color: #1a1a1a !important;
  border-bottom: 1px solid #2d2d2d !important;
  border-radius: 12px 12px 0 0 !important;
}

.ant-modal-title {
  color: #ffffff !important;
  font-weight: 600;
}

.ant-modal-body {
  color: #ffffff !important;
}

.ant-modal-footer {
  border-top: 1px solid #2d2d2d !important;
  border-radius: 0 0 12px 12px !important;
}

/* 标签页样式 */
.ant-tabs-tab {
  color: #cccccc !important;
}

.ant-tabs-tab:hover {
  color: #ffffff !important;
}

.ant-tabs-tab-active {
  color: #4facfe !important;
}

.ant-tabs-ink-bar {
  background-color: #4facfe !important;
}

.ant-tabs-content-holder {
  color: #ffffff !important;
}

/* 表格样式 */
.ant-table {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
}

.ant-table-thead > tr > th {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
  border-bottom: 1px solid #404040 !important;
}

.ant-table-tbody > tr > td {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
  border-bottom: 1px solid #2d2d2d !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: #2d2d2d !important;
}

/* 分页样式 */
.ant-pagination-item {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.ant-pagination-item:hover {
  border-color: #4facfe !important;
  color: #4facfe !important;
}

.ant-pagination-item-active {
  background-color: #4facfe !important;
  border-color: #4facfe !important;
  color: #ffffff !important;
}

.ant-pagination-prev,
.ant-pagination-next {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
}

.ant-pagination-prev:hover,
.ant-pagination-next:hover {
  border-color: #4facfe !important;
  color: #4facfe !important;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
  border-radius: 6px !important;
}

.ant-tooltip-arrow::before {
  background-color: #1a1a1a !important;
}

/* 下拉菜单样式 */
.ant-dropdown-menu {
  background-color: #1a1a1a !important;
  border: 1px solid #2d2d2d !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.ant-dropdown-menu-item {
  color: #ffffff !important;
}

.ant-dropdown-menu-item:hover {
  background-color: #2d2d2d !important;
}

.ant-dropdown-menu-item-selected {
  background-color: #4facfe !important;
  color: #ffffff !important;
}

/* 进度条样式 */
.ant-progress-bg {
  background-color: #4facfe !important;
}

.ant-progress-text {
  color: #ffffff !important;
}

/* 标签样式 */
.ant-tag {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
  color: #ffffff !important;
  border-radius: 6px !important;
}

.ant-tag-blue {
  background-color: rgba(79, 172, 254, 0.1) !important;
  border-color: #4facfe !important;
  color: #4facfe !important;
}

.ant-tag-green {
  background-color: rgba(82, 196, 26, 0.1) !important;
  border-color: #52c41a !important;
  color: #52c41a !important;
}

.ant-tag-red {
  background-color: rgba(255, 107, 107, 0.1) !important;
  border-color: #ff6b6b !important;
  color: #ff6b6b !important;
}

.ant-tag-orange {
  background-color: rgba(250, 173, 20, 0.1) !important;
  border-color: #faad14 !important;
  color: #faad14 !important;
}

/* 视频预览样式 - 深色主题 */
.video-preview {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 现代化卡片效果 */
.clip-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background: #1a1a1a;
  border: 1px solid #2d2d2d;
  border-radius: 12px;
}

.clip-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  border-color: #404040;
}

/* 项目卡片样式 */
.project-card {
  background: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 16px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
}

.project-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 12px 24px rgba(79, 172, 254, 0.2), 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(79, 172, 254, 0.5) !important;
  z-index: 10 !important;
}

.project-card:hover .card-overlay {
  opacity: 1 !important;
}

.project-card:hover .card-action-buttons {
  opacity: 1 !important;
}

.project-card .ant-card-body {
  background: #1a1a1a !important;
  color: #ffffff !important;
  padding: 20px !important;
}

.project-card .ant-card-cover {
  overflow: hidden !important;
}

.project-card .ant-card-cover > div {
  transition: transform 0.3s ease !important;
}

.project-card:hover .ant-card-cover > div {
  transform: scale(1.05) !important;
}

/* 卡片按钮悬停效果 */
.project-card .ant-btn:hover {
  background: #2d2d2d !important;
  border-color: #4facfe !important;
  color: #4facfe !important;
  transform: translateY(-1px) !important;
}

.project-card .ant-btn[style*="color: rgb(255, 107, 107)"]:hover {
  background: rgba(255, 107, 107, 0.1) !important;
  border-color: #ff6b6b !important;
  color: #ff6b6b !important;
}

/* 统计卡片动画 */
.project-card .ant-card-body > div > div[style*="background: rgb(38, 38, 38)"] {
  transition: all 0.3s ease !important;
}

.project-card:hover .ant-card-body > div > div[style*="background: rgb(38, 38, 38)"] {
  background: #333333 !important;
  transform: translateY(-2px) !important;
}

/* 上传区域样式 */
.upload-area {
  background: #262626 !important;
  border: 2px dashed #404040 !important;
  border-radius: 16px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  position: relative !important;
  overflow: hidden !important;
}

.upload-area::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(79, 172, 254, 0.1), transparent) !important;
  transition: left 0.5s ease !important;
}

.upload-area:hover {
  border-color: #4facfe !important;
  background: rgba(79, 172, 254, 0.08) !important;
  box-shadow: 0 8px 32px rgba(79, 172, 254, 0.2) !important;
}

.upload-area:hover::before {
  left: 100% !important;
}

.upload-area.dragover {
  border-color: #4facfe !important;
  background: rgba(79, 172, 254, 0.15) !important;
  box-shadow: 0 12px 40px rgba(79, 172, 254, 0.3) !important;
}

/* 上传按钮悬停效果 */
.upload-area + div button[style*="linear-gradient"]:hover {
  transform: translateY(-2px) scale(1.05) !important;
  box-shadow: 0 8px 24px rgba(79, 172, 254, 0.6) !important;
}

/* 状态标签样式 */
.status-tag {
  border-radius: 20px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-orange {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.gradient-bg-blue {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 毛玻璃效果 */
.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}