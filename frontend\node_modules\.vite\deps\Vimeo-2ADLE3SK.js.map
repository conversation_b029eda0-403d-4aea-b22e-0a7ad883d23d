{"version": 3, "sources": ["../../react-player/lib/players/Vimeo.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Vimeo_exports = {};\n__export(Vimeo_exports, {\n  default: () => Vimeo\n});\nmodule.exports = __toCommonJS(Vimeo_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://player.vimeo.com/api/player.js\";\nconst SDK_GLOBAL = \"Vimeo\";\nconst cleanUrl = (url) => {\n  return url.replace(\"/manage/videos\", \"\");\n};\nclass Vimeo extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    // Prevent checking isLoading when URL changes\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"duration\", null);\n    __publicField(this, \"currentTime\", null);\n    __publicField(this, \"secondsLoaded\", null);\n    __publicField(this, \"mute\", () => {\n      this.setMuted(true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.setMuted(false);\n    });\n    __publicField(this, \"ref\", (container) => {\n      this.container = container;\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url) {\n    this.duration = null;\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Vimeo2) => {\n      if (!this.container)\n        return;\n      const { playerOptions, title } = this.props.config;\n      this.player = new Vimeo2.Player(this.container, {\n        url: cleanUrl(url),\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        loop: this.props.loop,\n        playsinline: this.props.playsinline,\n        controls: this.props.controls,\n        ...playerOptions\n      });\n      this.player.ready().then(() => {\n        const iframe = this.container.querySelector(\"iframe\");\n        iframe.style.width = \"100%\";\n        iframe.style.height = \"100%\";\n        if (title) {\n          iframe.title = title;\n        }\n      }).catch(this.props.onError);\n      this.player.on(\"loaded\", () => {\n        this.props.onReady();\n        this.refreshDuration();\n      });\n      this.player.on(\"play\", () => {\n        this.props.onPlay();\n        this.refreshDuration();\n      });\n      this.player.on(\"pause\", this.props.onPause);\n      this.player.on(\"seeked\", (e) => this.props.onSeek(e.seconds));\n      this.player.on(\"ended\", this.props.onEnded);\n      this.player.on(\"error\", this.props.onError);\n      this.player.on(\"timeupdate\", ({ seconds }) => {\n        this.currentTime = seconds;\n      });\n      this.player.on(\"progress\", ({ seconds }) => {\n        this.secondsLoaded = seconds;\n      });\n      this.player.on(\"bufferstart\", this.props.onBuffer);\n      this.player.on(\"bufferend\", this.props.onBufferEnd);\n      this.player.on(\"playbackratechange\", (e) => this.props.onPlaybackRateChange(e.playbackRate));\n    }, this.props.onError);\n  }\n  refreshDuration() {\n    this.player.getDuration().then((duration) => {\n      this.duration = duration;\n    });\n  }\n  play() {\n    const promise = this.callPlayer(\"play\");\n    if (promise) {\n      promise.catch(this.props.onError);\n    }\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.callPlayer(\"unload\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"setCurrentTime\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  setMuted(muted) {\n    this.callPlayer(\"setMuted\", muted);\n  }\n  setLoop(loop) {\n    this.callPlayer(\"setLoop\", loop);\n  }\n  setPlaybackRate(rate) {\n    this.callPlayer(\"setPlaybackRate\", rate);\n  }\n  getDuration() {\n    return this.duration;\n  }\n  getCurrentTime() {\n    return this.currentTime;\n  }\n  getSecondsLoaded() {\n    return this.secondsLoaded;\n  }\n  render() {\n    const { display } = this.props;\n    const style = {\n      width: \"100%\",\n      height: \"100%\",\n      overflow: \"hidden\",\n      display\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\n      \"div\",\n      {\n        key: this.props.url,\n        ref: this.ref,\n        style\n      }\n    );\n  }\n}\n__publicField(Vimeo, \"displayName\", \"Vimeo\");\n__publicField(Vimeo, \"canPlay\", import_patterns.canPlay.vimeo);\n__publicField(Vimeo, \"forceLoad\", true);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,gBAAgB,CAAC;AACrB,aAAS,eAAe;AAAA,MACtB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,aAAa;AAC3C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,WAAW,CAAC,QAAQ;AACxB,aAAO,IAAI,QAAQ,kBAAkB,EAAE;AAAA,IACzC;AACA,QAAM,QAAN,cAAoB,aAAa,UAAU;AAAA,MACzC,cAAc;AACZ,cAAM,GAAG,SAAS;AAElB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,YAAY,IAAI;AACpC,sBAAc,MAAM,eAAe,IAAI;AACvC,sBAAc,MAAM,iBAAiB,IAAI;AACzC,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,SAAS,IAAI;AAAA,QACpB,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,SAAS,KAAK;AAAA,QACrB,CAAC;AACD,sBAAc,MAAM,OAAO,CAAC,cAAc;AACxC,eAAK,YAAY;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK;AACR,aAAK,WAAW;AAChB,SAAC,GAAG,aAAa,QAAQ,SAAS,UAAU,EAAE,KAAK,CAAC,WAAW;AAC7D,cAAI,CAAC,KAAK;AACR;AACF,gBAAM,EAAE,eAAe,MAAM,IAAI,KAAK,MAAM;AAC5C,eAAK,SAAS,IAAI,OAAO,OAAO,KAAK,WAAW;AAAA,YAC9C,KAAK,SAAS,GAAG;AAAA,YACjB,UAAU,KAAK,MAAM;AAAA,YACrB,OAAO,KAAK,MAAM;AAAA,YAClB,MAAM,KAAK,MAAM;AAAA,YACjB,aAAa,KAAK,MAAM;AAAA,YACxB,UAAU,KAAK,MAAM;AAAA,YACrB,GAAG;AAAA,UACL,CAAC;AACD,eAAK,OAAO,MAAM,EAAE,KAAK,MAAM;AAC7B,kBAAM,SAAS,KAAK,UAAU,cAAc,QAAQ;AACpD,mBAAO,MAAM,QAAQ;AACrB,mBAAO,MAAM,SAAS;AACtB,gBAAI,OAAO;AACT,qBAAO,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC,EAAE,MAAM,KAAK,MAAM,OAAO;AAC3B,eAAK,OAAO,GAAG,UAAU,MAAM;AAC7B,iBAAK,MAAM,QAAQ;AACnB,iBAAK,gBAAgB;AAAA,UACvB,CAAC;AACD,eAAK,OAAO,GAAG,QAAQ,MAAM;AAC3B,iBAAK,MAAM,OAAO;AAClB,iBAAK,gBAAgB;AAAA,UACvB,CAAC;AACD,eAAK,OAAO,GAAG,SAAS,KAAK,MAAM,OAAO;AAC1C,eAAK,OAAO,GAAG,UAAU,CAAC,MAAM,KAAK,MAAM,OAAO,EAAE,OAAO,CAAC;AAC5D,eAAK,OAAO,GAAG,SAAS,KAAK,MAAM,OAAO;AAC1C,eAAK,OAAO,GAAG,SAAS,KAAK,MAAM,OAAO;AAC1C,eAAK,OAAO,GAAG,cAAc,CAAC,EAAE,QAAQ,MAAM;AAC5C,iBAAK,cAAc;AAAA,UACrB,CAAC;AACD,eAAK,OAAO,GAAG,YAAY,CAAC,EAAE,QAAQ,MAAM;AAC1C,iBAAK,gBAAgB;AAAA,UACvB,CAAC;AACD,eAAK,OAAO,GAAG,eAAe,KAAK,MAAM,QAAQ;AACjD,eAAK,OAAO,GAAG,aAAa,KAAK,MAAM,WAAW;AAClD,eAAK,OAAO,GAAG,sBAAsB,CAAC,MAAM,KAAK,MAAM,qBAAqB,EAAE,YAAY,CAAC;AAAA,QAC7F,GAAG,KAAK,MAAM,OAAO;AAAA,MACvB;AAAA,MACA,kBAAkB;AAChB,aAAK,OAAO,YAAY,EAAE,KAAK,CAAC,aAAa;AAC3C,eAAK,WAAW;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,MACA,OAAO;AACL,cAAM,UAAU,KAAK,WAAW,MAAM;AACtC,YAAI,SAAS;AACX,kBAAQ,MAAM,KAAK,MAAM,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AACL,aAAK,WAAW,QAAQ;AAAA,MAC1B;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,WAAW,kBAAkB,OAAO;AACzC,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,aAAa,QAAQ;AAAA,MACvC;AAAA,MACA,SAAS,OAAO;AACd,aAAK,WAAW,YAAY,KAAK;AAAA,MACnC;AAAA,MACA,QAAQ,MAAM;AACZ,aAAK,WAAW,WAAW,IAAI;AAAA,MACjC;AAAA,MACA,gBAAgB,MAAM;AACpB,aAAK,WAAW,mBAAmB,IAAI;AAAA,MACzC;AAAA,MACA,cAAc;AACZ,eAAO,KAAK;AAAA,MACd;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK;AAAA,MACd;AAAA,MACA,mBAAmB;AACjB,eAAO,KAAK;AAAA,MACd;AAAA,MACA,SAAS;AACP,cAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,UACV;AAAA,QACF;AACA,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,KAAK,KAAK,MAAM;AAAA,YAChB,KAAK,KAAK;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,kBAAc,OAAO,eAAe,OAAO;AAC3C,kBAAc,OAAO,WAAW,gBAAgB,QAAQ,KAAK;AAC7D,kBAAc,OAAO,aAAa,IAAI;AAAA;AAAA;", "names": []}