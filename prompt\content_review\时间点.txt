# 内容解说视频时间点定位 Prompt

## 核心原则：解说完整、逻辑清晰、信息准确

1.  **解说完整性**：时间戳必须严格对应完整的解说单元和内容段落。
2.  **逻辑连贯性**：时间范围必须完整覆盖解说的逻辑链条，确保分析的完整性。
3.  **自然边界**：优先在情节转换、主题切换或解说段落的自然边界处定位时间点。

---

## 关键指令：如何确定 `start_time` 和 `end_time`

### `start_time` 的确定：
-   应定位到该解说话题的**第一句核心内容**的开始时间。
-   忽略该内容之前的无关过渡、背景音乐或片头片尾。
-   **优先选择**：在话题明确提出或解说开始的起始点，确保解说的完整性。
-   **内容解说特殊考虑**：如果有重要的背景介绍、作品片段或铺垫内容，应适当前移起始时间。

### `end_time` 的确定 (最重要)：
-   **必须**是覆盖该话题核心解说的**最后一句话的结束时间戳**。
-   **解说完整性**：确保情节解说、分析评价、技巧讲解等核心内容完整，不出现解说断层。
-   **自然结束**：优先选择在总结性语句、评价结论或话题转换处结束。
-   **信息保持**：如果解说末尾有重要的补充信息、彩蛋内容或作品片段，应包含在内。
-   如果解说话题的讨论一直持续到所提供SRT文本块的末尾，那么 `end_time` 就应该是最后一句相关字幕的结束时间戳。
-   **错误做法**：将 `end_time` 无脑设置为整个文本块的结束时间。这是绝对要避免的。

### 内容解说时长控制原则：
-   **目标时长**：每个解说话题片段应在3-8分钟之间
-   **复杂解说时长**：涉及深度分析或复杂情节的解说可适当延长至10分钟
-   **简单介绍**：基础背景介绍或简单概念可压缩至2-3分钟
-   **重要内容**：核心情节解说或重要分析可延长至12分钟

### 内容解说特殊处理：
-   **完整情节**：包含起承转合的完整情节解说应作为一个整体
-   **分析过程**：重要的分析过程和推理逻辑应完整保留
-   **作品片段**：与解说配套的作品片段应与解说内容同步
-   **技巧讲解**：技巧演示与理论讲解应配套出现，不可分离

---

## 输入格式
你将收到一个JSON对象，包含：
-   `outline`: 一个包含**多个**待处理解说话题的JSON数组。
-   `srt_text`: 与这批话题相关的**单个**SRT文本块，格式为 `序号\n开始 --> 结束\n文本\n\n`。

## 输出格式
-   严格按照输入大纲的结构，为**每个**话题对象补充 `start_time` 和 `end_time` 字段。
-   **关键：** 在输出时，请将输入的 `title` 字段重命名为 `outline`，并将 `subtopics` 字段重命名为 `content`。
-   最终输出一个包含**所有**处理后话题的JSON数组。
-   确保时间格式为 `HH:MM:SS,mmm`。

**严格的JSON输出要求：**
1. 输出必须以 `[` 开始，以 `]` 结束，不要添加任何解释文字、标题或Markdown代码块标记
2. 使用标准英文双引号 "，绝不使用中文引号 "" 或单引号
3. 确保所有括号、方括号正确匹配，对象间用逗号分隔，最后一个对象后不加逗号
4. 字符串中的引号必须转义为 \"
5. 不能包含任何注释、额外文本或控制字符
6. 确保JSON格式完全有效，可以被标准JSON解析器解析

## 内容解说时间定位示例

### 正确的时间定位：
- **情节解说**：从"接下来我们看这个关键情节..."开始，到"这就是这段情节的核心意义"结束
- **人物分析**：从"我们来分析一下这个角色..."开始，到"这就是角色的深层含义"结束
- **技巧讲解**：从技巧介绍开始，到演示结束和效果说明
- **背景介绍**：从背景说明开始，到背景与主题关联的总结

### 需要避免的错误：
- 在情节解说的关键分析处断开
- 将技巧讲解与实际演示分离
- 在人物分析的深度解读处截断
- 忽略重要的补充信息或作品片段

## 内容解说类型的特殊考虑

### 1. 影视解说类：
- 确保情节解说的完整性和逻辑性
- 保留重要的人物分析和关系解读
- 包含与解说配套的影片片段
- 确保技法分析的专业性和准确性

### 2. 游戏解说类：
- 保持游戏操作与解说的同步性
- 包含完整的攻略步骤和技巧演示
- 保留重要的游戏背景和世界观介绍
- 确保游戏体验的真实传达

### 3. 作品分析类：
- 包含完整的分析过程和推理逻辑
- 保持理论阐述与实例分析的结合
- 确保文化背景和历史语境的完整性
- 包含重要的价值判断和意义阐释

### 4. 科普解说类：
- 保持知识讲解的系统性和逻辑性
- 包含完整的实验过程和原理解释
- 确保概念解释的准确性和通俗性
- 保留重要的应用实例和拓展内容

## 不同解说内容的时间定位策略

### 情节导向解说：
- 按情节发展定位：背景设定-情节发展-高潮转折-结局解读
- 保持情节解说的完整性和连贯性
- 包含重要的情节分析和深层含义
- 确保观众对情节的完整理解

### 技巧导向解说：
- 按技巧类型定位：基础技巧-进阶技巧-高级应用-实战演示
- 保持技巧讲解的系统性和实用性
- 包含完整的操作演示和效果展示
- 确保技巧的可学习性和可复制性

### 分析导向解说：
- 按分析层次定位：表面现象-深层原因-影响意义-价值判断
- 保持分析过程的逻辑性和深度
- 包含重要的论证过程和结论总结
- 确保分析的客观性和说服力

### 知识导向解说：
- 按知识结构定位：基础概念-核心原理-应用实例-拓展延伸
- 保持知识传递的系统性和准确性
- 包含重要的概念解释和原理阐述
- 确保知识的实用性和可理解性

## 注意事项

1. **解说完整性**：确保每个片段包含完整的解说逻辑和内容结构
2. **信息准确性**：重要的事实信息和技术细节必须完整包含
3. **逻辑连贯性**：解说的逻辑链条和分析过程应保持完整
4. **观看体验**：考虑观众的理解能力和接受节奏
5. **价值传递**：确保解说的教育价值和娱乐价值的完整传达
6. **原作尊重**：在时间定位过程中保持对原作品的尊重
7. **版权意识**：注意作品片段的使用时长和版权问题