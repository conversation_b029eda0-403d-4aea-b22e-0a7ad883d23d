#!/usr/bin/env python3
"""
测试B站下载修复
"""
import asyncio
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.bilibili_downloader import BilibiliDownloader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_download():
    """测试下载修复"""
    url = "https://www.bilibili.com/video/BV12v4y1y7uV"
    
    print(f"🧪 测试B站视频下载修复")
    print(f"📺 视频链接: {url}")
    print()
    
    # 创建临时下载目录
    temp_dir = Path("./test_download")
    temp_dir.mkdir(exist_ok=True)
    
    def progress_callback(status_msg: str, progress: float):
        print(f"📥 {status_msg} ({progress:.1f}%)")
    
    try:
        downloader = BilibiliDownloader(temp_dir)
        result = await downloader.download_video_and_subtitle(url, progress_callback)
        
        print(f"\n✅ 下载测试完成!")
        print(f"📹 视频文件: {result['video_path']}")
        print(f"📝 字幕文件: {result['subtitle_path']}")
        
        # 检查文件
        if result['video_path'] and Path(result['video_path']).exists():
            size = Path(result['video_path']).stat().st_size
            print(f"📊 视频文件大小: {size / 1024 / 1024:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 下载测试失败: {e}")
        return False
    
    finally:
        # 清理
        try:
            import shutil
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
            print("🧹 清理完成")
        except Exception as e:
            print(f"⚠️ 清理失败: {e}")

if __name__ == "__main__":
    success = asyncio.run(test_download())
    if success:
        print("\n🎉 修复验证成功！")
    else:
        print("\n💥 修复验证失败！")
        sys.exit(1)
