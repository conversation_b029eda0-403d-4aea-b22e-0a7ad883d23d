#!/usr/bin/env python3
"""
调试字幕下载逻辑
"""
import subprocess
import os
import tempfile
from pathlib import Path

def test_subtitle_availability(url):
    """测试视频是否有字幕"""
    print(f"🔍 检查视频字幕可用性: {url}")
    
    cmd = [
        "yt-dlp",
        "--list-subs",
        url
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ 字幕信息:")
            print(result.stdout)
            return True
        else:
            print("❌ 获取字幕信息失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 检查字幕失败: {e}")
        return False

def test_subtitle_download(url):
    """测试字幕下载"""
    print(f"\n📥 测试字幕下载: {url}")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试不同的字幕下载配置
        configs = [
            {
                "name": "AI中文字幕",
                "cmd": [
                    "yt-dlp",
                    "--write-sub",
                    "--sub-lang", "ai-zh",
                    "--sub-format", "srt",
                    "--skip-download",  # 只下载字幕
                    "--output", "test_video.%(ext)s",
                    url
                ]
            },
            {
                "name": "所有中文字幕",
                "cmd": [
                    "yt-dlp",
                    "--write-sub",
                    "--sub-lang", "zh-Hans,zh-CN,zh",
                    "--sub-format", "srt",
                    "--skip-download",
                    "--output", "test_video2.%(ext)s",
                    url
                ]
            },
            {
                "name": "自动字幕",
                "cmd": [
                    "yt-dlp",
                    "--write-auto-sub",
                    "--sub-lang", "zh-Hans,zh-CN,zh",
                    "--sub-format", "srt",
                    "--skip-download",
                    "--output", "test_video3.%(ext)s",
                    url
                ]
            }
        ]
        
        for config in configs:
            print(f"\n🧪 测试配置: {config['name']}")
            print(f"命令: {' '.join(config['cmd'])}")
            
            try:
                result = subprocess.run(
                    config['cmd'], 
                    capture_output=True, 
                    text=True, 
                    timeout=60,
                    cwd=temp_path
                )
                
                if result.returncode == 0:
                    print("✅ 下载成功")
                    
                    # 列出下载的文件
                    files = list(temp_path.glob("*"))
                    if files:
                        print("📁 下载的文件:")
                        for file in files:
                            size = file.stat().st_size if file.is_file() else 0
                            print(f"   {file.name} ({size} bytes)")
                    else:
                        print("⚠️ 没有下载到文件")
                else:
                    print("❌ 下载失败:")
                    print(result.stderr)
                    
            except subprocess.TimeoutExpired:
                print("⏰ 下载超时")
            except Exception as e:
                print(f"❌ 下载异常: {e}")
            
            # 清理文件
            for file in temp_path.glob("*"):
                if file.is_file():
                    file.unlink()

def analyze_current_download_dir():
    """分析当前下载目录的结构"""
    print("\n📂 分析当前下载目录结构...")
    
    temp_downloads = Path("./temp_downloads")
    if not temp_downloads.exists():
        print("❌ temp_downloads 目录不存在")
        return
    
    print(f"📁 {temp_downloads} 目录内容:")
    for root, dirs, files in os.walk(temp_downloads):
        level = root.replace(str(temp_downloads), '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            file_path = Path(root) / file
            size = file_path.stat().st_size
            print(f"{subindent}{file} ({size} bytes)")

def main():
    """主函数"""
    url = "https://www.bilibili.com/video/BV12v4y1y7uV"
    
    print("🧪 字幕下载调试工具")
    print("=" * 50)
    
    # 1. 检查字幕可用性
    test_subtitle_availability(url)
    
    # 2. 测试字幕下载
    test_subtitle_download(url)
    
    # 3. 分析当前下载目录
    analyze_current_download_dir()
    
    print("\n💡 建议:")
    print("1. 如果没有AI字幕，尝试下载自动生成字幕")
    print("2. 检查视频是否真的有字幕")
    print("3. 考虑使用更宽泛的字幕语言设置")

if __name__ == "__main__":
    main()
