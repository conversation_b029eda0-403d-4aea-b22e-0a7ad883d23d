#!/usr/bin/env python3
"""
调试B站视频下载问题
"""
import asyncio
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.bilibili_downloader import BilibiliDownloader, get_bilibili_video_info

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def debug_bilibili_download():
    """调试B站视频下载"""
    url = "https://www.bilibili.com/video/BV12v4y1y7uV"
    
    print(f"🔍 开始调试B站视频下载")
    print(f"📺 视频链接: {url}")
    print()
    
    # 1. 验证URL格式
    print("1️⃣ 验证URL格式...")
    downloader = BilibiliDownloader()
    if downloader.validate_bilibili_url(url):
        print("✅ URL格式有效")
    else:
        print("❌ URL格式无效")
        return
    
    # 2. 获取视频信息
    print("\n2️⃣ 获取视频信息...")
    try:
        video_info = await get_bilibili_video_info(url)
        print(f"✅ 视频标题: {video_info.title}")
        print(f"✅ 视频时长: {video_info.duration}秒")
        print(f"✅ 上传者: {video_info.uploader}")
        print(f"✅ 播放量: {video_info.view_count}")
    except Exception as e:
        print(f"❌ 获取视频信息失败: {e}")
        return
    
    # 3. 测试下载（使用临时目录）
    print("\n3️⃣ 测试下载...")
    temp_dir = Path("./debug_download")
    temp_dir.mkdir(exist_ok=True)
    
    def progress_callback(status_msg: str, progress: float):
        print(f"📥 {status_msg} ({progress:.1f}%)")
    
    try:
        downloader = BilibiliDownloader(temp_dir)
        result = await downloader.download_video_and_subtitle(url, progress_callback)
        
        print(f"✅ 下载完成!")
        print(f"📹 视频文件: {result['video_path']}")
        print(f"📝 字幕文件: {result['subtitle_path']}")
        
        # 检查文件是否存在
        if result['video_path'] and Path(result['video_path']).exists():
            video_size = Path(result['video_path']).stat().st_size
            print(f"📊 视频文件大小: {video_size / 1024 / 1024:.2f} MB")
        else:
            print("⚠️ 视频文件不存在或路径为空")
            
        if result['subtitle_path'] and Path(result['subtitle_path']).exists():
            subtitle_size = Path(result['subtitle_path']).stat().st_size
            print(f"📊 字幕文件大小: {subtitle_size} bytes")
        else:
            print("⚠️ 字幕文件不存在或路径为空")
            
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print(f"🔍 错误类型: {type(e).__name__}")
        
        # 检查临时目录内容
        if temp_dir.exists():
            files = list(temp_dir.iterdir())
            print(f"📁 临时目录内容: {[f.name for f in files]}")
    
    # 4. 清理
    print("\n4️⃣ 清理临时文件...")
    try:
        import shutil
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        print("✅ 清理完成")
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

async def test_yt_dlp_directly():
    """直接测试yt-dlp命令"""
    print("\n🛠️ 直接测试yt-dlp命令...")
    
    import subprocess
    url = "https://www.bilibili.com/video/BV12v4y1y7uV"
    
    # 测试基本信息获取
    cmd_info = [
        "yt-dlp",
        "--print", "title",
        "--print", "duration",
        "--print", "uploader",
        url
    ]
    
    try:
        result = subprocess.run(cmd_info, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print("✅ yt-dlp信息获取成功:")
            print(result.stdout)
        else:
            print("❌ yt-dlp信息获取失败:")
            print(result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ yt-dlp命令超时")
    except Exception as e:
        print(f"❌ yt-dlp命令执行失败: {e}")

if __name__ == "__main__":
    print("🧪 B站视频下载调试工具\n")
    
    # 运行调试
    asyncio.run(debug_bilibili_download())
    
    # 直接测试yt-dlp
    asyncio.run(test_yt_dlp_directly())
