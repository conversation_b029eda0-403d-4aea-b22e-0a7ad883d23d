["tests/test_config.py::TestAPIConfig::test_api_config_custom_values", "tests/test_config.py::TestAPIConfig::test_api_config_defaults", "tests/test_config.py::TestConfigManager::test_config_manager_initialization", "tests/test_config.py::TestConfigManager::test_export_config", "tests/test_config.py::TestConfigManager::test_get_api_config", "tests/test_config.py::TestConfigManager::test_get_path_config", "tests/test_config.py::TestConfigManager::test_get_processing_config", "tests/test_config.py::TestConfigManager::test_update_api_key", "tests/test_config.py::TestLegacyConfig::test_legacy_config_compatibility", "tests/test_config.py::TestPathConfig::test_path_config_custom_values", "tests/test_config.py::TestPathConfig::test_path_config_defaults", "tests/test_config.py::TestProcessingConfig::test_processing_config_custom_values", "tests/test_config.py::TestProcessingConfig::test_processing_config_defaults", "tests/test_config.py::TestSettings::test_settings_default_values", "tests/test_config.py::TestSettings::test_settings_initialization", "tests/test_config.py::TestSettings::test_settings_validation"]