{"version": 3, "sources": ["../../react-player/lib/players/index.js", "../../react-fast-compare/index.js", "../../react-player/lib/props.js", "../../react-player/lib/Player.js", "../../react-player/lib/ReactPlayer.js", "../../react-player/lib/index.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar players_exports = {};\n__export(players_exports, {\n  default: () => players_default\n});\nmodule.exports = __toCommonJS(players_exports);\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nvar players_default = [\n  {\n    key: \"youtube\",\n    name: \"YouTube\",\n    canPlay: import_patterns.canPlay.youtube,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerYouTube' */\n      \"./YouTube\"\n    ))\n  },\n  {\n    key: \"soundcloud\",\n    name: \"SoundCloud\",\n    canPlay: import_patterns.canPlay.soundcloud,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerSoundCloud' */\n      \"./SoundCloud\"\n    ))\n  },\n  {\n    key: \"vimeo\",\n    name: \"Vimeo\",\n    canPlay: import_patterns.canPlay.vimeo,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerVimeo' */\n      \"./Vimeo\"\n    ))\n  },\n  {\n    key: \"mux\",\n    name: \"Mux\",\n    canPlay: import_patterns.canPlay.mux,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerMux' */\n      \"./Mux\"\n    ))\n  },\n  {\n    key: \"facebook\",\n    name: \"Facebook\",\n    canPlay: import_patterns.canPlay.facebook,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerFacebook' */\n      \"./Facebook\"\n    ))\n  },\n  {\n    key: \"streamable\",\n    name: \"Streamable\",\n    canPlay: import_patterns.canPlay.streamable,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerStreamable' */\n      \"./Streamable\"\n    ))\n  },\n  {\n    key: \"wistia\",\n    name: \"Wistia\",\n    canPlay: import_patterns.canPlay.wistia,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerWistia' */\n      \"./Wistia\"\n    ))\n  },\n  {\n    key: \"twitch\",\n    name: \"Twitch\",\n    canPlay: import_patterns.canPlay.twitch,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerTwitch' */\n      \"./Twitch\"\n    ))\n  },\n  {\n    key: \"dailymotion\",\n    name: \"DailyMotion\",\n    canPlay: import_patterns.canPlay.dailymotion,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerDailyMotion' */\n      \"./DailyMotion\"\n    ))\n  },\n  {\n    key: \"mixcloud\",\n    name: \"Mixcloud\",\n    canPlay: import_patterns.canPlay.mixcloud,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerMixcloud' */\n      \"./Mixcloud\"\n    ))\n  },\n  {\n    key: \"vidyard\",\n    name: \"Vidyard\",\n    canPlay: import_patterns.canPlay.vidyard,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerVidyard' */\n      \"./Vidyard\"\n    ))\n  },\n  {\n    key: \"kaltura\",\n    name: \"Kaltura\",\n    canPlay: import_patterns.canPlay.kaltura,\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerKaltura' */\n      \"./Kaltura\"\n    ))\n  },\n  {\n    key: \"file\",\n    name: \"FilePlayer\",\n    canPlay: import_patterns.canPlay.file,\n    canEnablePIP: (url) => {\n      return import_patterns.canPlay.file(url) && (document.pictureInPictureEnabled || (0, import_utils.supportsWebKitPresentationMode)()) && !import_patterns.AUDIO_EXTENSIONS.test(url);\n    },\n    lazyPlayer: (0, import_utils.lazy)(() => import(\n      /* webpackChunkName: 'reactPlayerFilePlayer' */\n      \"./FilePlayer\"\n    ))\n  }\n];\n", "/* global Map:readonly, Set:readonly, ArrayBuffer:readonly */\n\nvar hasElementType = typeof Element !== 'undefined';\nvar hasMap = typeof Map === 'function';\nvar hasSet = typeof Set === 'function';\nvar hasArrayBuffer = typeof ArrayBuffer === 'function' && !!ArrayBuffer.isView;\n\n// Note: We **don't** need `envHasBigInt64Array` in fde es6/index.js\n\nfunction equal(a, b) {\n  // START: fast-deep-equal es6/index.js 3.1.3\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    // START: Modifications:\n    // 1. Extra `has<Type> &&` helpers in initial condition allow es6 code\n    //    to co-exist with es5.\n    // 2. Replace `for of` with es5 compliant iteration using `for`.\n    //    Basically, take:\n    //\n    //    ```js\n    //    for (i of a.entries())\n    //      if (!b.has(i[0])) return false;\n    //    ```\n    //\n    //    ... and convert to:\n    //\n    //    ```js\n    //    it = a.entries();\n    //    while (!(i = it.next()).done)\n    //      if (!b.has(i.value[0])) return false;\n    //    ```\n    //\n    //    **Note**: `i` access switches to `i.value`.\n    var it;\n    if (hasMap && (a instanceof Map) && (b instanceof Map)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!equal(i.value[1], b.get(i.value[0]))) return false;\n      return true;\n    }\n\n    if (hasSet && (a instanceof Set) && (b instanceof Set)) {\n      if (a.size !== b.size) return false;\n      it = a.entries();\n      while (!(i = it.next()).done)\n        if (!b.has(i.value[0])) return false;\n      return true;\n    }\n    // END: Modifications\n\n    if (hasArrayBuffer && ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (a[i] !== b[i]) return false;\n      return true;\n    }\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    // START: Modifications:\n    // Apply guards for `Object.create(null)` handling. See:\n    // - https://github.com/FormidableLabs/react-fast-compare/issues/64\n    // - https://github.com/epoberezkin/fast-deep-equal/issues/49\n    if (a.valueOf !== Object.prototype.valueOf && typeof a.valueOf === 'function' && typeof b.valueOf === 'function') return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString && typeof a.toString === 'function' && typeof b.toString === 'function') return a.toString() === b.toString();\n    // END: Modifications\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    // END: fast-deep-equal\n\n    // START: react-fast-compare\n    // custom handling for DOM elements\n    if (hasElementType && a instanceof Element) return false;\n\n    // custom handling for React/Preact\n    for (i = length; i-- !== 0;) {\n      if ((keys[i] === '_owner' || keys[i] === '__v' || keys[i] === '__o') && a.$$typeof) {\n        // React-specific: avoid traversing React elements' _owner\n        // Preact-specific: avoid traversing Preact elements' __v and __o\n        //    __v = $_original / $_vnode\n        //    __o = $_owner\n        // These properties contain circular references and are not needed when\n        // comparing the actual elements (and not their owners)\n        // .$$typeof and ._store on just reasonable markers of elements\n\n        continue;\n      }\n\n      // all other properties should be traversed as usual\n      if (!equal(a[keys[i]], b[keys[i]])) return false;\n    }\n    // END: react-fast-compare\n\n    // START: fast-deep-equal\n    return true;\n  }\n\n  return a !== a && b !== b;\n}\n// end fast-deep-equal\n\nmodule.exports = function isEqual(a, b) {\n  try {\n    return equal(a, b);\n  } catch (error) {\n    if (((error.message || '').match(/stack|recursion/i))) {\n      // warn on circular references, don't crash\n      // browsers give this different errors name and messages:\n      // chrome/safari: \"RangeError\", \"Maximum call stack size exceeded\"\n      // firefox: \"InternalError\", too much recursion\"\n      // edge: \"Error\", \"Out of stack space\"\n      console.warn('react-fast-compare cannot handle circular refs');\n      return false;\n    }\n    // some other error. we should definitely know about these\n    throw error;\n  }\n};\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar props_exports = {};\n__export(props_exports, {\n  defaultProps: () => defaultProps,\n  propTypes: () => propTypes\n});\nmodule.exports = __toCommonJS(props_exports);\nvar import_prop_types = __toESM(require(\"prop-types\"));\nconst { string, bool, number, array, oneOfType, shape, object, func, node } = import_prop_types.default;\nconst propTypes = {\n  url: oneOfType([string, array, object]),\n  playing: bool,\n  loop: bool,\n  controls: bool,\n  volume: number,\n  muted: bool,\n  playbackRate: number,\n  width: oneOfType([string, number]),\n  height: oneOfType([string, number]),\n  style: object,\n  progressInterval: number,\n  playsinline: bool,\n  pip: bool,\n  stopOnUnmount: bool,\n  light: oneOfType([bool, string, object]),\n  playIcon: node,\n  previewTabIndex: number,\n  previewAriaLabel: string,\n  fallback: node,\n  oEmbedUrl: string,\n  wrapper: oneOfType([\n    string,\n    func,\n    shape({ render: func.isRequired })\n  ]),\n  config: shape({\n    soundcloud: shape({\n      options: object\n    }),\n    youtube: shape({\n      playerVars: object,\n      embedOptions: object,\n      onUnstarted: func\n    }),\n    facebook: shape({\n      appId: string,\n      version: string,\n      playerId: string,\n      attributes: object\n    }),\n    dailymotion: shape({\n      params: object\n    }),\n    vimeo: shape({\n      playerOptions: object,\n      title: string\n    }),\n    mux: shape({\n      attributes: object,\n      version: string\n    }),\n    file: shape({\n      attributes: object,\n      tracks: array,\n      forceVideo: bool,\n      forceAudio: bool,\n      forceHLS: bool,\n      forceSafariHLS: bool,\n      forceDisableHls: bool,\n      forceDASH: bool,\n      forceFLV: bool,\n      hlsOptions: object,\n      hlsVersion: string,\n      dashVersion: string,\n      flvVersion: string\n    }),\n    wistia: shape({\n      options: object,\n      playerId: string,\n      customControls: array\n    }),\n    mixcloud: shape({\n      options: object\n    }),\n    twitch: shape({\n      options: object,\n      playerId: string\n    }),\n    vidyard: shape({\n      options: object\n    })\n  }),\n  onReady: func,\n  onStart: func,\n  onPlay: func,\n  onPause: func,\n  onBuffer: func,\n  onBufferEnd: func,\n  onEnded: func,\n  onError: func,\n  onDuration: func,\n  onSeek: func,\n  onPlaybackRateChange: func,\n  onPlaybackQualityChange: func,\n  onProgress: func,\n  onClickPreview: func,\n  onEnablePIP: func,\n  onDisablePIP: func\n};\nconst noop = () => {\n};\nconst defaultProps = {\n  playing: false,\n  loop: false,\n  controls: false,\n  volume: null,\n  muted: false,\n  playbackRate: 1,\n  width: \"640px\",\n  height: \"360px\",\n  style: {},\n  progressInterval: 1e3,\n  playsinline: false,\n  pip: false,\n  stopOnUnmount: true,\n  light: false,\n  fallback: null,\n  wrapper: \"div\",\n  previewTabIndex: 0,\n  previewAriaLabel: \"\",\n  oEmbedUrl: \"https://noembed.com/embed?url={url}\",\n  config: {\n    soundcloud: {\n      options: {\n        visual: true,\n        // Undocumented, but makes player fill container and look better\n        buying: false,\n        liking: false,\n        download: false,\n        sharing: false,\n        show_comments: false,\n        show_playcount: false\n      }\n    },\n    youtube: {\n      playerVars: {\n        playsinline: 1,\n        showinfo: 0,\n        rel: 0,\n        iv_load_policy: 3,\n        modestbranding: 1\n      },\n      embedOptions: {},\n      onUnstarted: noop\n    },\n    facebook: {\n      appId: \"1309697205772819\",\n      version: \"v3.3\",\n      playerId: null,\n      attributes: {}\n    },\n    dailymotion: {\n      params: {\n        api: 1,\n        \"endscreen-enable\": false\n      }\n    },\n    vimeo: {\n      playerOptions: {\n        autopause: false,\n        byline: false,\n        portrait: false,\n        title: false\n      },\n      title: null\n    },\n    mux: {\n      attributes: {},\n      version: \"2\"\n    },\n    file: {\n      attributes: {},\n      tracks: [],\n      forceVideo: false,\n      forceAudio: false,\n      forceHLS: false,\n      forceDASH: false,\n      forceFLV: false,\n      hlsOptions: {},\n      hlsVersion: \"1.1.4\",\n      dashVersion: \"3.1.3\",\n      flvVersion: \"1.5.0\",\n      forceDisableHls: false\n    },\n    wistia: {\n      options: {},\n      playerId: null,\n      customControls: null\n    },\n    mixcloud: {\n      options: {\n        hide_cover: 1\n      }\n    },\n    twitch: {\n      options: {},\n      playerId: null\n    },\n    vidyard: {\n      options: {}\n    }\n  },\n  onReady: noop,\n  onStart: noop,\n  onPlay: noop,\n  onPause: noop,\n  onBuffer: noop,\n  onBufferEnd: noop,\n  onEnded: noop,\n  onError: noop,\n  onDuration: noop,\n  onSeek: noop,\n  onPlaybackRateChange: noop,\n  onPlaybackQualityChange: noop,\n  onProgress: noop,\n  onClickPreview: noop,\n  onEnablePIP: noop,\n  onDisablePIP: noop\n};\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Player_exports = {};\n__export(Player_exports, {\n  default: () => Player\n});\nmodule.exports = __toCommonJS(Player_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_react_fast_compare = __toESM(require(\"react-fast-compare\"));\nvar import_props = require(\"./props\");\nvar import_utils = require(\"./utils\");\nconst SEEK_ON_PLAY_EXPIRY = 5e3;\nclass Player extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"mounted\", false);\n    __publicField(this, \"isReady\", false);\n    __publicField(this, \"isPlaying\", false);\n    // Track playing state internally to prevent bugs\n    __publicField(this, \"isLoading\", true);\n    // Use isLoading to prevent onPause when switching URL\n    __publicField(this, \"loadOnReady\", null);\n    __publicField(this, \"startOnPlay\", true);\n    __publicField(this, \"seekOnPlay\", null);\n    __publicField(this, \"onDurationCalled\", false);\n    __publicField(this, \"handlePlayerMount\", (player) => {\n      if (this.player) {\n        this.progress();\n        return;\n      }\n      this.player = player;\n      this.player.load(this.props.url);\n      this.progress();\n    });\n    __publicField(this, \"getInternalPlayer\", (key) => {\n      if (!this.player)\n        return null;\n      return this.player[key];\n    });\n    __publicField(this, \"progress\", () => {\n      if (this.props.url && this.player && this.isReady) {\n        const playedSeconds = this.getCurrentTime() || 0;\n        const loadedSeconds = this.getSecondsLoaded();\n        const duration = this.getDuration();\n        if (duration) {\n          const progress = {\n            playedSeconds,\n            played: playedSeconds / duration\n          };\n          if (loadedSeconds !== null) {\n            progress.loadedSeconds = loadedSeconds;\n            progress.loaded = loadedSeconds / duration;\n          }\n          if (progress.playedSeconds !== this.prevPlayed || progress.loadedSeconds !== this.prevLoaded) {\n            this.props.onProgress(progress);\n          }\n          this.prevPlayed = progress.playedSeconds;\n          this.prevLoaded = progress.loadedSeconds;\n        }\n      }\n      this.progressTimeout = setTimeout(this.progress, this.props.progressFrequency || this.props.progressInterval);\n    });\n    __publicField(this, \"handleReady\", () => {\n      if (!this.mounted)\n        return;\n      this.isReady = true;\n      this.isLoading = false;\n      const { onReady, playing, volume, muted } = this.props;\n      onReady();\n      if (!muted && volume !== null) {\n        this.player.setVolume(volume);\n      }\n      if (this.loadOnReady) {\n        this.player.load(this.loadOnReady, true);\n        this.loadOnReady = null;\n      } else if (playing) {\n        this.player.play();\n      }\n      this.handleDurationCheck();\n    });\n    __publicField(this, \"handlePlay\", () => {\n      this.isPlaying = true;\n      this.isLoading = false;\n      const { onStart, onPlay, playbackRate } = this.props;\n      if (this.startOnPlay) {\n        if (this.player.setPlaybackRate && playbackRate !== 1) {\n          this.player.setPlaybackRate(playbackRate);\n        }\n        onStart();\n        this.startOnPlay = false;\n      }\n      onPlay();\n      if (this.seekOnPlay) {\n        this.seekTo(this.seekOnPlay);\n        this.seekOnPlay = null;\n      }\n      this.handleDurationCheck();\n    });\n    __publicField(this, \"handlePause\", (e) => {\n      this.isPlaying = false;\n      if (!this.isLoading) {\n        this.props.onPause(e);\n      }\n    });\n    __publicField(this, \"handleEnded\", () => {\n      const { activePlayer, loop, onEnded } = this.props;\n      if (activePlayer.loopOnEnded && loop) {\n        this.seekTo(0);\n      }\n      if (!loop) {\n        this.isPlaying = false;\n        onEnded();\n      }\n    });\n    __publicField(this, \"handleError\", (...args) => {\n      this.isLoading = false;\n      this.props.onError(...args);\n    });\n    __publicField(this, \"handleDurationCheck\", () => {\n      clearTimeout(this.durationCheckTimeout);\n      const duration = this.getDuration();\n      if (duration) {\n        if (!this.onDurationCalled) {\n          this.props.onDuration(duration);\n          this.onDurationCalled = true;\n        }\n      } else {\n        this.durationCheckTimeout = setTimeout(this.handleDurationCheck, 100);\n      }\n    });\n    __publicField(this, \"handleLoaded\", () => {\n      this.isLoading = false;\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n  }\n  componentWillUnmount() {\n    clearTimeout(this.progressTimeout);\n    clearTimeout(this.durationCheckTimeout);\n    if (this.isReady && this.props.stopOnUnmount) {\n      this.player.stop();\n      if (this.player.disablePIP) {\n        this.player.disablePIP();\n      }\n    }\n    this.mounted = false;\n  }\n  componentDidUpdate(prevProps) {\n    if (!this.player) {\n      return;\n    }\n    const { url, playing, volume, muted, playbackRate, pip, loop, activePlayer, disableDeferredLoading } = this.props;\n    if (!(0, import_react_fast_compare.default)(prevProps.url, url)) {\n      if (this.isLoading && !activePlayer.forceLoad && !disableDeferredLoading && !(0, import_utils.isMediaStream)(url)) {\n        console.warn(`ReactPlayer: the attempt to load ${url} is being deferred until the player has loaded`);\n        this.loadOnReady = url;\n        return;\n      }\n      this.isLoading = true;\n      this.startOnPlay = true;\n      this.onDurationCalled = false;\n      this.player.load(url, this.isReady);\n    }\n    if (!prevProps.playing && playing && !this.isPlaying) {\n      this.player.play();\n    }\n    if (prevProps.playing && !playing && this.isPlaying) {\n      this.player.pause();\n    }\n    if (!prevProps.pip && pip && this.player.enablePIP) {\n      this.player.enablePIP();\n    }\n    if (prevProps.pip && !pip && this.player.disablePIP) {\n      this.player.disablePIP();\n    }\n    if (prevProps.volume !== volume && volume !== null) {\n      this.player.setVolume(volume);\n    }\n    if (prevProps.muted !== muted) {\n      if (muted) {\n        this.player.mute();\n      } else {\n        this.player.unmute();\n        if (volume !== null) {\n          setTimeout(() => this.player.setVolume(volume));\n        }\n      }\n    }\n    if (prevProps.playbackRate !== playbackRate && this.player.setPlaybackRate) {\n      this.player.setPlaybackRate(playbackRate);\n    }\n    if (prevProps.loop !== loop && this.player.setLoop) {\n      this.player.setLoop(loop);\n    }\n  }\n  getDuration() {\n    if (!this.isReady)\n      return null;\n    return this.player.getDuration();\n  }\n  getCurrentTime() {\n    if (!this.isReady)\n      return null;\n    return this.player.getCurrentTime();\n  }\n  getSecondsLoaded() {\n    if (!this.isReady)\n      return null;\n    return this.player.getSecondsLoaded();\n  }\n  seekTo(amount, type, keepPlaying) {\n    if (!this.isReady) {\n      if (amount !== 0) {\n        this.seekOnPlay = amount;\n        setTimeout(() => {\n          this.seekOnPlay = null;\n        }, SEEK_ON_PLAY_EXPIRY);\n      }\n      return;\n    }\n    const isFraction = !type ? amount > 0 && amount < 1 : type === \"fraction\";\n    if (isFraction) {\n      const duration = this.player.getDuration();\n      if (!duration) {\n        console.warn(\"ReactPlayer: could not seek using fraction \\u2013\\xA0duration not yet available\");\n        return;\n      }\n      this.player.seekTo(duration * amount, keepPlaying);\n      return;\n    }\n    this.player.seekTo(amount, keepPlaying);\n  }\n  render() {\n    const Player2 = this.props.activePlayer;\n    if (!Player2) {\n      return null;\n    }\n    return /* @__PURE__ */ import_react.default.createElement(\n      Player2,\n      {\n        ...this.props,\n        onMount: this.handlePlayerMount,\n        onReady: this.handleReady,\n        onPlay: this.handlePlay,\n        onPause: this.handlePause,\n        onEnded: this.handleEnded,\n        onLoaded: this.handleLoaded,\n        onError: this.handleError\n      }\n    );\n  }\n}\n__publicField(Player, \"displayName\", \"Player\");\n__publicField(Player, \"propTypes\", import_props.propTypes);\n__publicField(Player, \"defaultProps\", import_props.defaultProps);\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar ReactPlayer_exports = {};\n__export(ReactPlayer_exports, {\n  createReactPlayer: () => createReactPlayer\n});\nmodule.exports = __toCommonJS(ReactPlayer_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_deepmerge = __toESM(require(\"deepmerge\"));\nvar import_memoize_one = __toESM(require(\"memoize-one\"));\nvar import_react_fast_compare = __toESM(require(\"react-fast-compare\"));\nvar import_props = require(\"./props\");\nvar import_utils = require(\"./utils\");\nvar import_Player = __toESM(require(\"./Player\"));\nconst Preview = (0, import_utils.lazy)(() => import(\n  /* webpackChunkName: 'reactPlayerPreview' */\n  \"./Preview\"\n));\nconst IS_BROWSER = typeof window !== \"undefined\" && window.document && typeof document !== \"undefined\";\nconst IS_GLOBAL = typeof global !== \"undefined\" && global.window && global.window.document;\nconst SUPPORTED_PROPS = Object.keys(import_props.propTypes);\nconst UniversalSuspense = IS_BROWSER || IS_GLOBAL ? import_react.Suspense : () => null;\nconst customPlayers = [];\nconst createReactPlayer = (players, fallback) => {\n  var _a;\n  return _a = class extends import_react.Component {\n    constructor() {\n      super(...arguments);\n      __publicField(this, \"state\", {\n        showPreview: !!this.props.light\n      });\n      // Use references, as refs is used by React\n      __publicField(this, \"references\", {\n        wrapper: (wrapper) => {\n          this.wrapper = wrapper;\n        },\n        player: (player) => {\n          this.player = player;\n        }\n      });\n      __publicField(this, \"handleClickPreview\", (e) => {\n        this.setState({ showPreview: false });\n        this.props.onClickPreview(e);\n      });\n      __publicField(this, \"showPreview\", () => {\n        this.setState({ showPreview: true });\n      });\n      __publicField(this, \"getDuration\", () => {\n        if (!this.player)\n          return null;\n        return this.player.getDuration();\n      });\n      __publicField(this, \"getCurrentTime\", () => {\n        if (!this.player)\n          return null;\n        return this.player.getCurrentTime();\n      });\n      __publicField(this, \"getSecondsLoaded\", () => {\n        if (!this.player)\n          return null;\n        return this.player.getSecondsLoaded();\n      });\n      __publicField(this, \"getInternalPlayer\", (key = \"player\") => {\n        if (!this.player)\n          return null;\n        return this.player.getInternalPlayer(key);\n      });\n      __publicField(this, \"seekTo\", (fraction, type, keepPlaying) => {\n        if (!this.player)\n          return null;\n        this.player.seekTo(fraction, type, keepPlaying);\n      });\n      __publicField(this, \"handleReady\", () => {\n        this.props.onReady(this);\n      });\n      __publicField(this, \"getActivePlayer\", (0, import_memoize_one.default)((url) => {\n        for (const player of [...customPlayers, ...players]) {\n          if (player.canPlay(url)) {\n            return player;\n          }\n        }\n        if (fallback) {\n          return fallback;\n        }\n        return null;\n      }));\n      __publicField(this, \"getConfig\", (0, import_memoize_one.default)((url, key) => {\n        const { config } = this.props;\n        return import_deepmerge.default.all([\n          import_props.defaultProps.config,\n          import_props.defaultProps.config[key] || {},\n          config,\n          config[key] || {}\n        ]);\n      }));\n      __publicField(this, \"getAttributes\", (0, import_memoize_one.default)((url) => {\n        return (0, import_utils.omit)(this.props, SUPPORTED_PROPS);\n      }));\n      __publicField(this, \"renderActivePlayer\", (url) => {\n        if (!url)\n          return null;\n        const player = this.getActivePlayer(url);\n        if (!player)\n          return null;\n        const config = this.getConfig(url, player.key);\n        return /* @__PURE__ */ import_react.default.createElement(\n          import_Player.default,\n          {\n            ...this.props,\n            key: player.key,\n            ref: this.references.player,\n            config,\n            activePlayer: player.lazyPlayer || player,\n            onReady: this.handleReady\n          }\n        );\n      });\n    }\n    shouldComponentUpdate(nextProps, nextState) {\n      return !(0, import_react_fast_compare.default)(this.props, nextProps) || !(0, import_react_fast_compare.default)(this.state, nextState);\n    }\n    componentDidUpdate(prevProps) {\n      const { light } = this.props;\n      if (!prevProps.light && light) {\n        this.setState({ showPreview: true });\n      }\n      if (prevProps.light && !light) {\n        this.setState({ showPreview: false });\n      }\n    }\n    renderPreview(url) {\n      if (!url)\n        return null;\n      const { light, playIcon, previewTabIndex, oEmbedUrl, previewAriaLabel } = this.props;\n      return /* @__PURE__ */ import_react.default.createElement(\n        Preview,\n        {\n          url,\n          light,\n          playIcon,\n          previewTabIndex,\n          previewAriaLabel,\n          oEmbedUrl,\n          onClick: this.handleClickPreview\n        }\n      );\n    }\n    render() {\n      const { url, style, width, height, fallback: fallback2, wrapper: Wrapper } = this.props;\n      const { showPreview } = this.state;\n      const attributes = this.getAttributes(url);\n      const wrapperRef = typeof Wrapper === \"string\" ? this.references.wrapper : void 0;\n      return /* @__PURE__ */ import_react.default.createElement(Wrapper, { ref: wrapperRef, style: { ...style, width, height }, ...attributes }, /* @__PURE__ */ import_react.default.createElement(UniversalSuspense, { fallback: fallback2 }, showPreview ? this.renderPreview(url) : this.renderActivePlayer(url)));\n    }\n  }, __publicField(_a, \"displayName\", \"ReactPlayer\"), __publicField(_a, \"propTypes\", import_props.propTypes), __publicField(_a, \"defaultProps\", import_props.defaultProps), __publicField(_a, \"addCustomPlayer\", (player) => {\n    customPlayers.push(player);\n  }), __publicField(_a, \"removeCustomPlayers\", () => {\n    customPlayers.length = 0;\n  }), __publicField(_a, \"canPlay\", (url) => {\n    for (const Player2 of [...customPlayers, ...players]) {\n      if (Player2.canPlay(url)) {\n        return true;\n      }\n    }\n    return false;\n  }), __publicField(_a, \"canEnablePIP\", (url) => {\n    for (const Player2 of [...customPlayers, ...players]) {\n      if (Player2.canEnablePIP && Player2.canEnablePIP(url)) {\n        return true;\n      }\n    }\n    return false;\n  }), _a;\n};\n", "var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar src_exports = {};\n__export(src_exports, {\n  default: () => src_default\n});\nmodule.exports = __toCommonJS(src_exports);\nvar import_players = __toESM(require(\"./players\"));\nvar import_ReactPlayer = require(\"./ReactPlayer\");\nconst fallback = import_players.default[import_players.default.length - 1];\nvar src_default = (0, import_ReactPlayer.createReactPlayer)(import_players.default, fallback);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AACA,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAE/B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AASA,QAAIA,gBAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,kBAAkB,CAAC;AACvB,aAAS,iBAAiB;AAAA,MACxB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAUA,cAAa,eAAe;AAC7C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAI,kBAAkB;AAAA,MACpB;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,MAAM;AAAA,QACN,SAAS,gBAAgB,QAAQ;AAAA,QACjC,cAAc,CAAC,QAAQ;AACrB,iBAAO,gBAAgB,QAAQ,KAAK,GAAG,MAAM,SAAS,4BAA4B,GAAG,aAAa,gCAAgC,MAAM,CAAC,gBAAgB,iBAAiB,KAAK,GAAG;AAAA,QACpL;AAAA,QACA,aAAa,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,UAEvC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;;;AC3JA;AAAA;AAEA,QAAI,iBAAiB,OAAO,YAAY;AACxC,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,iBAAiB,OAAO,gBAAgB,cAAc,CAAC,CAAC,YAAY;AAIxE,aAAS,MAAM,GAAG,GAAG;AAEnB,UAAI,MAAM,EAAG,QAAO;AAEpB,UAAI,KAAK,KAAK,OAAO,KAAK,YAAY,OAAO,KAAK,UAAU;AAC1D,YAAI,EAAE,gBAAgB,EAAE,YAAa,QAAO;AAE5C,YAAI,QAAQ,GAAG;AACf,YAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAsBA,YAAI;AACJ,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAG,QAAO;AACjC,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,EAAG,QAAO;AACpD,iBAAO;AAAA,QACT;AAEA,YAAI,UAAW,aAAa,OAAS,aAAa,KAAM;AACtD,cAAI,EAAE,SAAS,EAAE,KAAM,QAAO;AAC9B,eAAK,EAAE,QAAQ;AACf,iBAAO,EAAE,IAAI,GAAG,KAAK,GAAG;AACtB,gBAAI,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,EAAG,QAAO;AACjC,iBAAO;AAAA,QACT;AAGA,YAAI,kBAAkB,YAAY,OAAO,CAAC,KAAK,YAAY,OAAO,CAAC,GAAG;AACpE,mBAAS,EAAE;AACX,cAAI,UAAU,EAAE,OAAQ,QAAO;AAC/B,eAAK,IAAI,QAAQ,QAAQ;AACvB,gBAAI,EAAE,CAAC,MAAM,EAAE,CAAC,EAAG,QAAO;AAC5B,iBAAO;AAAA,QACT;AAEA,YAAI,EAAE,gBAAgB,OAAQ,QAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE;AAK5E,YAAI,EAAE,YAAY,OAAO,UAAU,WAAW,OAAO,EAAE,YAAY,cAAc,OAAO,EAAE,YAAY,WAAY,QAAO,EAAE,QAAQ,MAAM,EAAE,QAAQ;AACnJ,YAAI,EAAE,aAAa,OAAO,UAAU,YAAY,OAAO,EAAE,aAAa,cAAc,OAAO,EAAE,aAAa,WAAY,QAAO,EAAE,SAAS,MAAM,EAAE,SAAS;AAGzJ,eAAO,OAAO,KAAK,CAAC;AACpB,iBAAS,KAAK;AACd,YAAI,WAAW,OAAO,KAAK,CAAC,EAAE,OAAQ,QAAO;AAE7C,aAAK,IAAI,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,UAAU,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,EAAG,QAAO;AAKhE,YAAI,kBAAkB,aAAa,QAAS,QAAO;AAGnD,aAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,eAAK,KAAK,CAAC,MAAM,YAAY,KAAK,CAAC,MAAM,SAAS,KAAK,CAAC,MAAM,UAAU,EAAE,UAAU;AASlF;AAAA,UACF;AAGA,cAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAG,QAAO;AAAA,QAC7C;AAIA,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,MAAM;AAAA,IAC1B;AAGA,WAAO,UAAU,SAAS,QAAQ,GAAG,GAAG;AACtC,UAAI;AACF,eAAO,MAAM,GAAG,CAAC;AAAA,MACnB,SAAS,OAAO;AACd,aAAM,MAAM,WAAW,IAAI,MAAM,kBAAkB,GAAI;AAMrD,kBAAQ,KAAK,gDAAgD;AAC7D,iBAAO;AAAA,QACT;AAEA,cAAM;AAAA,MACR;AAAA,IACF;AAAA;AAAA;;;AC1IA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAIC,gBAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC;AACrB,aAAS,eAAe;AAAA,MACtB,cAAc,MAAM;AAAA,MACpB,WAAW,MAAM;AAAA,IACnB,CAAC;AACD,WAAO,UAAUA,cAAa,aAAa;AAC3C,QAAI,oBAAoB,QAAQ,oBAAqB;AACrD,QAAM,EAAE,QAAQ,MAAM,QAAQ,OAAO,WAAW,OAAO,QAAQ,MAAM,KAAK,IAAI,kBAAkB;AAChG,QAAM,YAAY;AAAA,MAChB,KAAK,UAAU,CAAC,QAAQ,OAAO,MAAM,CAAC;AAAA,MACtC,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,cAAc;AAAA,MACd,OAAO,UAAU,CAAC,QAAQ,MAAM,CAAC;AAAA,MACjC,QAAQ,UAAU,CAAC,QAAQ,MAAM,CAAC;AAAA,MAClC,OAAO;AAAA,MACP,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,KAAK;AAAA,MACL,eAAe;AAAA,MACf,OAAO,UAAU,CAAC,MAAM,QAAQ,MAAM,CAAC;AAAA,MACvC,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,SAAS,UAAU;AAAA,QACjB;AAAA,QACA;AAAA,QACA,MAAM,EAAE,QAAQ,KAAK,WAAW,CAAC;AAAA,MACnC,CAAC;AAAA,MACD,QAAQ,MAAM;AAAA,QACZ,YAAY,MAAM;AAAA,UAChB,SAAS;AAAA,QACX,CAAC;AAAA,QACD,SAAS,MAAM;AAAA,UACb,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,aAAa;AAAA,QACf,CAAC;AAAA,QACD,UAAU,MAAM;AAAA,UACd,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,UACV,YAAY;AAAA,QACd,CAAC;AAAA,QACD,aAAa,MAAM;AAAA,UACjB,QAAQ;AAAA,QACV,CAAC;AAAA,QACD,OAAO,MAAM;AAAA,UACX,eAAe;AAAA,UACf,OAAO;AAAA,QACT,CAAC;AAAA,QACD,KAAK,MAAM;AAAA,UACT,YAAY;AAAA,UACZ,SAAS;AAAA,QACX,CAAC;AAAA,QACD,MAAM,MAAM;AAAA,UACV,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,UACjB,WAAW;AAAA,UACX,UAAU;AAAA,UACV,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,YAAY;AAAA,QACd,CAAC;AAAA,QACD,QAAQ,MAAM;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,UACV,gBAAgB;AAAA,QAClB,CAAC;AAAA,QACD,UAAU,MAAM;AAAA,UACd,SAAS;AAAA,QACX,CAAC;AAAA,QACD,QAAQ,MAAM;AAAA,UACZ,SAAS;AAAA,UACT,UAAU;AAAA,QACZ,CAAC;AAAA,QACD,SAAS,MAAM;AAAA,UACb,SAAS;AAAA,QACX,CAAC;AAAA,MACH,CAAC;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,yBAAyB;AAAA,MACzB,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AACA,QAAM,OAAO,MAAM;AAAA,IACnB;AACA,QAAM,eAAe;AAAA,MACnB,SAAS;AAAA,MACT,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,cAAc;AAAA,MACd,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO,CAAC;AAAA,MACR,kBAAkB;AAAA,MAClB,aAAa;AAAA,MACb,KAAK;AAAA,MACL,eAAe;AAAA,MACf,OAAO;AAAA,MACP,UAAU;AAAA,MACV,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX,QAAQ;AAAA,QACN,YAAY;AAAA,UACV,SAAS;AAAA,YACP,QAAQ;AAAA;AAAA,YAER,QAAQ;AAAA,YACR,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,SAAS;AAAA,YACT,eAAe;AAAA,YACf,gBAAgB;AAAA,UAClB;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,YAAY;AAAA,YACV,aAAa;AAAA,YACb,UAAU;AAAA,YACV,KAAK;AAAA,YACL,gBAAgB;AAAA,YAChB,gBAAgB;AAAA,UAClB;AAAA,UACA,cAAc,CAAC;AAAA,UACf,aAAa;AAAA,QACf;AAAA,QACA,UAAU;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA,UACT,UAAU;AAAA,UACV,YAAY,CAAC;AAAA,QACf;AAAA,QACA,aAAa;AAAA,UACX,QAAQ;AAAA,YACN,KAAK;AAAA,YACL,oBAAoB;AAAA,UACtB;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,eAAe;AAAA,YACb,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA,QACT;AAAA,QACA,KAAK;AAAA,UACH,YAAY,CAAC;AAAA,UACb,SAAS;AAAA,QACX;AAAA,QACA,MAAM;AAAA,UACJ,YAAY,CAAC;AAAA,UACb,QAAQ,CAAC;AAAA,UACT,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,UAAU;AAAA,UACV,YAAY,CAAC;AAAA,UACb,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,iBAAiB;AAAA,QACnB;AAAA,QACA,QAAQ;AAAA,UACN,SAAS,CAAC;AAAA,UACV,UAAU;AAAA,UACV,gBAAgB;AAAA,QAClB;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,YACP,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS,CAAC;AAAA,UACV,UAAU;AAAA,QACZ;AAAA,QACA,SAAS;AAAA,UACP,SAAS,CAAC;AAAA,QACZ;AAAA,MACF;AAAA,MACA,SAAS;AAAA,MACT,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,sBAAsB;AAAA,MACtB,yBAAyB;AAAA,MACzB,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAAA;AAAA;;;AC9PA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAIC,gBAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,CAAC;AACtB,aAAS,gBAAgB;AAAA,MACvB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAUA,cAAa,cAAc;AAC5C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,4BAA4B,QAAQ,4BAA6B;AACrE,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAM,sBAAsB;AAC5B,QAAM,SAAN,cAAqB,aAAa,UAAU;AAAA,MAC1C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,WAAW,KAAK;AACpC,sBAAc,MAAM,WAAW,KAAK;AACpC,sBAAc,MAAM,aAAa,KAAK;AAEtC,sBAAc,MAAM,aAAa,IAAI;AAErC,sBAAc,MAAM,eAAe,IAAI;AACvC,sBAAc,MAAM,eAAe,IAAI;AACvC,sBAAc,MAAM,cAAc,IAAI;AACtC,sBAAc,MAAM,oBAAoB,KAAK;AAC7C,sBAAc,MAAM,qBAAqB,CAAC,WAAW;AACnD,cAAI,KAAK,QAAQ;AACf,iBAAK,SAAS;AACd;AAAA,UACF;AACA,eAAK,SAAS;AACd,eAAK,OAAO,KAAK,KAAK,MAAM,GAAG;AAC/B,eAAK,SAAS;AAAA,QAChB,CAAC;AACD,sBAAc,MAAM,qBAAqB,CAAC,QAAQ;AAChD,cAAI,CAAC,KAAK;AACR,mBAAO;AACT,iBAAO,KAAK,OAAO,GAAG;AAAA,QACxB,CAAC;AACD,sBAAc,MAAM,YAAY,MAAM;AACpC,cAAI,KAAK,MAAM,OAAO,KAAK,UAAU,KAAK,SAAS;AACjD,kBAAM,gBAAgB,KAAK,eAAe,KAAK;AAC/C,kBAAM,gBAAgB,KAAK,iBAAiB;AAC5C,kBAAM,WAAW,KAAK,YAAY;AAClC,gBAAI,UAAU;AACZ,oBAAM,WAAW;AAAA,gBACf;AAAA,gBACA,QAAQ,gBAAgB;AAAA,cAC1B;AACA,kBAAI,kBAAkB,MAAM;AAC1B,yBAAS,gBAAgB;AACzB,yBAAS,SAAS,gBAAgB;AAAA,cACpC;AACA,kBAAI,SAAS,kBAAkB,KAAK,cAAc,SAAS,kBAAkB,KAAK,YAAY;AAC5F,qBAAK,MAAM,WAAW,QAAQ;AAAA,cAChC;AACA,mBAAK,aAAa,SAAS;AAC3B,mBAAK,aAAa,SAAS;AAAA,YAC7B;AAAA,UACF;AACA,eAAK,kBAAkB,WAAW,KAAK,UAAU,KAAK,MAAM,qBAAqB,KAAK,MAAM,gBAAgB;AAAA,QAC9G,CAAC;AACD,sBAAc,MAAM,eAAe,MAAM;AACvC,cAAI,CAAC,KAAK;AACR;AACF,eAAK,UAAU;AACf,eAAK,YAAY;AACjB,gBAAM,EAAE,SAAS,SAAS,QAAQ,MAAM,IAAI,KAAK;AACjD,kBAAQ;AACR,cAAI,CAAC,SAAS,WAAW,MAAM;AAC7B,iBAAK,OAAO,UAAU,MAAM;AAAA,UAC9B;AACA,cAAI,KAAK,aAAa;AACpB,iBAAK,OAAO,KAAK,KAAK,aAAa,IAAI;AACvC,iBAAK,cAAc;AAAA,UACrB,WAAW,SAAS;AAClB,iBAAK,OAAO,KAAK;AAAA,UACnB;AACA,eAAK,oBAAoB;AAAA,QAC3B,CAAC;AACD,sBAAc,MAAM,cAAc,MAAM;AACtC,eAAK,YAAY;AACjB,eAAK,YAAY;AACjB,gBAAM,EAAE,SAAS,QAAQ,aAAa,IAAI,KAAK;AAC/C,cAAI,KAAK,aAAa;AACpB,gBAAI,KAAK,OAAO,mBAAmB,iBAAiB,GAAG;AACrD,mBAAK,OAAO,gBAAgB,YAAY;AAAA,YAC1C;AACA,oBAAQ;AACR,iBAAK,cAAc;AAAA,UACrB;AACA,iBAAO;AACP,cAAI,KAAK,YAAY;AACnB,iBAAK,OAAO,KAAK,UAAU;AAC3B,iBAAK,aAAa;AAAA,UACpB;AACA,eAAK,oBAAoB;AAAA,QAC3B,CAAC;AACD,sBAAc,MAAM,eAAe,CAAC,MAAM;AACxC,eAAK,YAAY;AACjB,cAAI,CAAC,KAAK,WAAW;AACnB,iBAAK,MAAM,QAAQ,CAAC;AAAA,UACtB;AAAA,QACF,CAAC;AACD,sBAAc,MAAM,eAAe,MAAM;AACvC,gBAAM,EAAE,cAAc,MAAM,QAAQ,IAAI,KAAK;AAC7C,cAAI,aAAa,eAAe,MAAM;AACpC,iBAAK,OAAO,CAAC;AAAA,UACf;AACA,cAAI,CAAC,MAAM;AACT,iBAAK,YAAY;AACjB,oBAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,sBAAc,MAAM,eAAe,IAAI,SAAS;AAC9C,eAAK,YAAY;AACjB,eAAK,MAAM,QAAQ,GAAG,IAAI;AAAA,QAC5B,CAAC;AACD,sBAAc,MAAM,uBAAuB,MAAM;AAC/C,uBAAa,KAAK,oBAAoB;AACtC,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,UAAU;AACZ,gBAAI,CAAC,KAAK,kBAAkB;AAC1B,mBAAK,MAAM,WAAW,QAAQ;AAC9B,mBAAK,mBAAmB;AAAA,YAC1B;AAAA,UACF,OAAO;AACL,iBAAK,uBAAuB,WAAW,KAAK,qBAAqB,GAAG;AAAA,UACtE;AAAA,QACF,CAAC;AACD,sBAAc,MAAM,gBAAgB,MAAM;AACxC,eAAK,YAAY;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,uBAAuB;AACrB,qBAAa,KAAK,eAAe;AACjC,qBAAa,KAAK,oBAAoB;AACtC,YAAI,KAAK,WAAW,KAAK,MAAM,eAAe;AAC5C,eAAK,OAAO,KAAK;AACjB,cAAI,KAAK,OAAO,YAAY;AAC1B,iBAAK,OAAO,WAAW;AAAA,UACzB;AAAA,QACF;AACA,aAAK,UAAU;AAAA,MACjB;AAAA,MACA,mBAAmB,WAAW;AAC5B,YAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,QACF;AACA,cAAM,EAAE,KAAK,SAAS,QAAQ,OAAO,cAAc,KAAK,MAAM,cAAc,uBAAuB,IAAI,KAAK;AAC5G,YAAI,EAAE,GAAG,0BAA0B,SAAS,UAAU,KAAK,GAAG,GAAG;AAC/D,cAAI,KAAK,aAAa,CAAC,aAAa,aAAa,CAAC,0BAA0B,EAAE,GAAG,aAAa,eAAe,GAAG,GAAG;AACjH,oBAAQ,KAAK,oCAAoC,GAAG,gDAAgD;AACpG,iBAAK,cAAc;AACnB;AAAA,UACF;AACA,eAAK,YAAY;AACjB,eAAK,cAAc;AACnB,eAAK,mBAAmB;AACxB,eAAK,OAAO,KAAK,KAAK,KAAK,OAAO;AAAA,QACpC;AACA,YAAI,CAAC,UAAU,WAAW,WAAW,CAAC,KAAK,WAAW;AACpD,eAAK,OAAO,KAAK;AAAA,QACnB;AACA,YAAI,UAAU,WAAW,CAAC,WAAW,KAAK,WAAW;AACnD,eAAK,OAAO,MAAM;AAAA,QACpB;AACA,YAAI,CAAC,UAAU,OAAO,OAAO,KAAK,OAAO,WAAW;AAClD,eAAK,OAAO,UAAU;AAAA,QACxB;AACA,YAAI,UAAU,OAAO,CAAC,OAAO,KAAK,OAAO,YAAY;AACnD,eAAK,OAAO,WAAW;AAAA,QACzB;AACA,YAAI,UAAU,WAAW,UAAU,WAAW,MAAM;AAClD,eAAK,OAAO,UAAU,MAAM;AAAA,QAC9B;AACA,YAAI,UAAU,UAAU,OAAO;AAC7B,cAAI,OAAO;AACT,iBAAK,OAAO,KAAK;AAAA,UACnB,OAAO;AACL,iBAAK,OAAO,OAAO;AACnB,gBAAI,WAAW,MAAM;AACnB,yBAAW,MAAM,KAAK,OAAO,UAAU,MAAM,CAAC;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AACA,YAAI,UAAU,iBAAiB,gBAAgB,KAAK,OAAO,iBAAiB;AAC1E,eAAK,OAAO,gBAAgB,YAAY;AAAA,QAC1C;AACA,YAAI,UAAU,SAAS,QAAQ,KAAK,OAAO,SAAS;AAClD,eAAK,OAAO,QAAQ,IAAI;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,cAAc;AACZ,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,eAAO,KAAK,OAAO,YAAY;AAAA,MACjC;AAAA,MACA,iBAAiB;AACf,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,eAAO,KAAK,OAAO,eAAe;AAAA,MACpC;AAAA,MACA,mBAAmB;AACjB,YAAI,CAAC,KAAK;AACR,iBAAO;AACT,eAAO,KAAK,OAAO,iBAAiB;AAAA,MACtC;AAAA,MACA,OAAO,QAAQ,MAAM,aAAa;AAChC,YAAI,CAAC,KAAK,SAAS;AACjB,cAAI,WAAW,GAAG;AAChB,iBAAK,aAAa;AAClB,uBAAW,MAAM;AACf,mBAAK,aAAa;AAAA,YACpB,GAAG,mBAAmB;AAAA,UACxB;AACA;AAAA,QACF;AACA,cAAM,aAAa,CAAC,OAAO,SAAS,KAAK,SAAS,IAAI,SAAS;AAC/D,YAAI,YAAY;AACd,gBAAM,WAAW,KAAK,OAAO,YAAY;AACzC,cAAI,CAAC,UAAU;AACb,oBAAQ,KAAK,yEAAiF;AAC9F;AAAA,UACF;AACA,eAAK,OAAO,OAAO,WAAW,QAAQ,WAAW;AACjD;AAAA,QACF;AACA,aAAK,OAAO,OAAO,QAAQ,WAAW;AAAA,MACxC;AAAA,MACA,SAAS;AACP,cAAM,UAAU,KAAK,MAAM;AAC3B,YAAI,CAAC,SAAS;AACZ,iBAAO;AAAA,QACT;AACA,eAAuB,aAAa,QAAQ;AAAA,UAC1C;AAAA,UACA;AAAA,YACE,GAAG,KAAK;AAAA,YACR,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,QAAQ,KAAK;AAAA,YACb,SAAS,KAAK;AAAA,YACd,SAAS,KAAK;AAAA,YACd,UAAU,KAAK;AAAA,YACf,SAAS,KAAK;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,kBAAc,QAAQ,eAAe,QAAQ;AAC7C,kBAAc,QAAQ,aAAa,aAAa,SAAS;AACzD,kBAAc,QAAQ,gBAAgB,aAAa,YAAY;AAAA;AAAA;;;AC7R/D;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAIC,gBAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,sBAAsB,CAAC;AAC3B,aAAS,qBAAqB;AAAA,MAC5B,mBAAmB,MAAM;AAAA,IAC3B,CAAC;AACD,WAAO,UAAUA,cAAa,mBAAmB;AACjD,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,mBAAmB,QAAQ,aAAoB;AACnD,QAAI,qBAAqB,QAAQ,+DAAsB;AACvD,QAAI,4BAA4B,QAAQ,4BAA6B;AACrE,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,gBAAgB,QAAQ,gBAAmB;AAC/C,QAAM,WAAW,GAAG,aAAa,MAAM,MAAM;AAAA;AAAA,MAE3C;AAAA,IACF,CAAC;AACD,QAAM,aAAa,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,aAAa;AAC3F,QAAM,YAAY,OAAO,WAAW,eAAe,OAAO,UAAU,OAAO,OAAO;AAClF,QAAM,kBAAkB,OAAO,KAAK,aAAa,SAAS;AAC1D,QAAM,oBAAoB,cAAc,YAAY,aAAa,WAAW,MAAM;AAClF,QAAM,gBAAgB,CAAC;AACvB,QAAM,oBAAoB,CAAC,SAAS,aAAa;AAC/C,UAAI;AACJ,aAAO,KAAK,cAAc,aAAa,UAAU;AAAA,QAC/C,cAAc;AACZ,gBAAM,GAAG,SAAS;AAClB,wBAAc,MAAM,SAAS;AAAA,YAC3B,aAAa,CAAC,CAAC,KAAK,MAAM;AAAA,UAC5B,CAAC;AAED,wBAAc,MAAM,cAAc;AAAA,YAChC,SAAS,CAAC,YAAY;AACpB,mBAAK,UAAU;AAAA,YACjB;AAAA,YACA,QAAQ,CAAC,WAAW;AAClB,mBAAK,SAAS;AAAA,YAChB;AAAA,UACF,CAAC;AACD,wBAAc,MAAM,sBAAsB,CAAC,MAAM;AAC/C,iBAAK,SAAS,EAAE,aAAa,MAAM,CAAC;AACpC,iBAAK,MAAM,eAAe,CAAC;AAAA,UAC7B,CAAC;AACD,wBAAc,MAAM,eAAe,MAAM;AACvC,iBAAK,SAAS,EAAE,aAAa,KAAK,CAAC;AAAA,UACrC,CAAC;AACD,wBAAc,MAAM,eAAe,MAAM;AACvC,gBAAI,CAAC,KAAK;AACR,qBAAO;AACT,mBAAO,KAAK,OAAO,YAAY;AAAA,UACjC,CAAC;AACD,wBAAc,MAAM,kBAAkB,MAAM;AAC1C,gBAAI,CAAC,KAAK;AACR,qBAAO;AACT,mBAAO,KAAK,OAAO,eAAe;AAAA,UACpC,CAAC;AACD,wBAAc,MAAM,oBAAoB,MAAM;AAC5C,gBAAI,CAAC,KAAK;AACR,qBAAO;AACT,mBAAO,KAAK,OAAO,iBAAiB;AAAA,UACtC,CAAC;AACD,wBAAc,MAAM,qBAAqB,CAAC,MAAM,aAAa;AAC3D,gBAAI,CAAC,KAAK;AACR,qBAAO;AACT,mBAAO,KAAK,OAAO,kBAAkB,GAAG;AAAA,UAC1C,CAAC;AACD,wBAAc,MAAM,UAAU,CAAC,UAAU,MAAM,gBAAgB;AAC7D,gBAAI,CAAC,KAAK;AACR,qBAAO;AACT,iBAAK,OAAO,OAAO,UAAU,MAAM,WAAW;AAAA,UAChD,CAAC;AACD,wBAAc,MAAM,eAAe,MAAM;AACvC,iBAAK,MAAM,QAAQ,IAAI;AAAA,UACzB,CAAC;AACD,wBAAc,MAAM,oBAAoB,GAAG,mBAAmB,SAAS,CAAC,QAAQ;AAC9E,uBAAW,UAAU,CAAC,GAAG,eAAe,GAAG,OAAO,GAAG;AACnD,kBAAI,OAAO,QAAQ,GAAG,GAAG;AACvB,uBAAO;AAAA,cACT;AAAA,YACF;AACA,gBAAI,UAAU;AACZ,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT,CAAC,CAAC;AACF,wBAAc,MAAM,cAAc,GAAG,mBAAmB,SAAS,CAAC,KAAK,QAAQ;AAC7E,kBAAM,EAAE,OAAO,IAAI,KAAK;AACxB,mBAAO,iBAAiB,QAAQ,IAAI;AAAA,cAClC,aAAa,aAAa;AAAA,cAC1B,aAAa,aAAa,OAAO,GAAG,KAAK,CAAC;AAAA,cAC1C;AAAA,cACA,OAAO,GAAG,KAAK,CAAC;AAAA,YAClB,CAAC;AAAA,UACH,CAAC,CAAC;AACF,wBAAc,MAAM,kBAAkB,GAAG,mBAAmB,SAAS,CAAC,QAAQ;AAC5E,oBAAQ,GAAG,aAAa,MAAM,KAAK,OAAO,eAAe;AAAA,UAC3D,CAAC,CAAC;AACF,wBAAc,MAAM,sBAAsB,CAAC,QAAQ;AACjD,gBAAI,CAAC;AACH,qBAAO;AACT,kBAAM,SAAS,KAAK,gBAAgB,GAAG;AACvC,gBAAI,CAAC;AACH,qBAAO;AACT,kBAAM,SAAS,KAAK,UAAU,KAAK,OAAO,GAAG;AAC7C,mBAAuB,aAAa,QAAQ;AAAA,cAC1C,cAAc;AAAA,cACd;AAAA,gBACE,GAAG,KAAK;AAAA,gBACR,KAAK,OAAO;AAAA,gBACZ,KAAK,KAAK,WAAW;AAAA,gBACrB;AAAA,gBACA,cAAc,OAAO,cAAc;AAAA,gBACnC,SAAS,KAAK;AAAA,cAChB;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,sBAAsB,WAAW,WAAW;AAC1C,iBAAO,EAAE,GAAG,0BAA0B,SAAS,KAAK,OAAO,SAAS,KAAK,EAAE,GAAG,0BAA0B,SAAS,KAAK,OAAO,SAAS;AAAA,QACxI;AAAA,QACA,mBAAmB,WAAW;AAC5B,gBAAM,EAAE,MAAM,IAAI,KAAK;AACvB,cAAI,CAAC,UAAU,SAAS,OAAO;AAC7B,iBAAK,SAAS,EAAE,aAAa,KAAK,CAAC;AAAA,UACrC;AACA,cAAI,UAAU,SAAS,CAAC,OAAO;AAC7B,iBAAK,SAAS,EAAE,aAAa,MAAM,CAAC;AAAA,UACtC;AAAA,QACF;AAAA,QACA,cAAc,KAAK;AACjB,cAAI,CAAC;AACH,mBAAO;AACT,gBAAM,EAAE,OAAO,UAAU,iBAAiB,WAAW,iBAAiB,IAAI,KAAK;AAC/E,iBAAuB,aAAa,QAAQ;AAAA,YAC1C;AAAA,YACA;AAAA,cACE;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,cACA,SAAS,KAAK;AAAA,YAChB;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AACP,gBAAM,EAAE,KAAK,OAAO,OAAO,QAAQ,UAAU,WAAW,SAAS,QAAQ,IAAI,KAAK;AAClF,gBAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,gBAAM,aAAa,KAAK,cAAc,GAAG;AACzC,gBAAM,aAAa,OAAO,YAAY,WAAW,KAAK,WAAW,UAAU;AAC3E,iBAAuB,aAAa,QAAQ,cAAc,SAAS,EAAE,KAAK,YAAY,OAAO,EAAE,GAAG,OAAO,OAAO,OAAO,GAAG,GAAG,WAAW,GAAmB,aAAa,QAAQ,cAAc,mBAAmB,EAAE,UAAU,UAAU,GAAG,cAAc,KAAK,cAAc,GAAG,IAAI,KAAK,mBAAmB,GAAG,CAAC,CAAC;AAAA,QACjT;AAAA,MACF,GAAG,cAAc,IAAI,eAAe,aAAa,GAAG,cAAc,IAAI,aAAa,aAAa,SAAS,GAAG,cAAc,IAAI,gBAAgB,aAAa,YAAY,GAAG,cAAc,IAAI,mBAAmB,CAAC,WAAW;AACzN,sBAAc,KAAK,MAAM;AAAA,MAC3B,CAAC,GAAG,cAAc,IAAI,uBAAuB,MAAM;AACjD,sBAAc,SAAS;AAAA,MACzB,CAAC,GAAG,cAAc,IAAI,WAAW,CAAC,QAAQ;AACxC,mBAAW,WAAW,CAAC,GAAG,eAAe,GAAG,OAAO,GAAG;AACpD,cAAI,QAAQ,QAAQ,GAAG,GAAG;AACxB,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC,GAAG,cAAc,IAAI,gBAAgB,CAAC,QAAQ;AAC7C,mBAAW,WAAW,CAAC,GAAG,eAAe,GAAG,OAAO,GAAG;AACpD,cAAI,QAAQ,gBAAgB,QAAQ,aAAa,GAAG,GAAG;AACrD,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC,GAAG;AAAA,IACN;AAAA;AAAA;;;AC3MA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAIC,gBAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,cAAc,CAAC;AACnB,aAAS,aAAa;AAAA,MACpB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAUA,cAAa,WAAW;AACzC,QAAI,iBAAiB,QAAQ,iBAAoB;AACjD,QAAI,qBAAqB;AACzB,QAAM,WAAW,eAAe,QAAQ,eAAe,QAAQ,SAAS,CAAC;AACzE,QAAI,eAAe,GAAG,mBAAmB,mBAAmB,eAAe,SAAS,QAAQ;AAAA;AAAA;", "names": ["__toCommonJS", "__toCommonJS", "__toCommonJS", "__toCommonJS", "__toCommonJS"]}