#!/usr/bin/env python3
"""
Cookie管理工具 - 帮助解决B站字幕下载的Cookie问题
"""
import subprocess
import os
import sys
from pathlib import Path

def check_browser_processes():
    """检查浏览器进程"""
    try:
        import psutil
        browsers = {
            'chrome': [],
            'firefox': [],
            'edge': [],
            'safari': []
        }
        
        for proc in psutil.process_iter(['pid', 'name']):
            name = proc.info['name'].lower() if proc.info['name'] else ''
            for browser in browsers:
                if browser in name:
                    browsers[browser].append(proc.info)
        
        return browsers
    except ImportError:
        print("⚠️ 需要安装 psutil: pip install psutil")
        return {}
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return {}

def test_browser_cookies():
    """测试各浏览器Cookie访问"""
    test_url = "https://www.bilibili.com/video/BV12v4y1y7uV"
    browsers = ["chrome", "firefox", "edge"]
    
    print("🧪 测试浏览器Cookie访问...")
    results = {}
    
    for browser in browsers:
        print(f"\n🔍 测试 {browser.title()}...")
        
        cmd = [
            "yt-dlp",
            "--cookies-from-browser", browser,
            "--simulate",
            "--print", "title",
            test_url
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ {browser.title()} Cookie 访问成功")
                results[browser] = "success"
            else:
                error = result.stderr.lower()
                if "could not copy" in error and "cookie database" in error:
                    print(f"🔒 {browser.title()} Cookie 数据库被锁定")
                    results[browser] = "locked"
                elif "could not find" in error:
                    print(f"❌ {browser.title()} 未安装或无Cookie数据")
                    results[browser] = "not_found"
                elif "failed to decrypt" in error:
                    print(f"🔐 {browser.title()} Cookie 解密失败")
                    results[browser] = "decrypt_failed"
                else:
                    print(f"❌ {browser.title()} Cookie 访问失败")
                    results[browser] = "failed"
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ {browser.title()} 测试超时")
            results[browser] = "timeout"
        except Exception as e:
            print(f"❌ {browser.title()} 测试异常: {e}")
            results[browser] = "error"
    
    return results

def show_chrome_fix_guide():
    """显示Chrome修复指南"""
    print("\n🔧 Chrome Cookie 修复指南")
    print("=" * 50)
    
    print("\n方法1: 关闭Chrome浏览器")
    print("  1. 完全关闭所有Chrome窗口")
    print("  2. 检查任务管理器，结束所有chrome.exe进程")
    print("  3. 重新运行下载任务")
    
    print("\n方法2: 使用Chrome启动参数")
    print("  1. 右键Chrome桌面快捷方式 → 属性")
    print("  2. 在'目标'字段末尾添加:")
    print("     --disable-features=LockProfileCookieDatabase")
    print("  3. 完整示例:")
    print('     "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --disable-features=LockProfileCookieDatabase')
    print("  4. 保存并使用修改后的快捷方式启动Chrome")
    
    print("\n方法3: 手动导出Cookie")
    print("  1. 安装浏览器插件: 'Get cookies.txt LOCALLY'")
    print("  2. 访问 bilibili.com 并登录")
    print("  3. 使用插件导出 cookies.txt 文件")
    print("  4. 将文件放在项目目录中")
    
    print("\n方法4: 使用其他浏览器")
    print("  1. 安装Firefox浏览器")
    print("  2. 登录bilibili.com")
    print("  3. 项目会自动尝试使用Firefox的Cookie")

def create_chrome_shortcut():
    """创建带参数的Chrome快捷方式"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "Chrome (Cookie Fix).lnk")
        
        # 查找Chrome安装路径
        chrome_paths = [
            "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
            "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe")
        ]
        
        chrome_exe = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_exe = path
                break
        
        if not chrome_exe:
            print("❌ 未找到Chrome安装路径")
            return False
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = chrome_exe
        shortcut.Arguments = "--disable-features=LockProfileCookieDatabase"
        shortcut.WorkingDirectory = os.path.dirname(chrome_exe)
        shortcut.IconLocation = chrome_exe
        shortcut.save()
        
        print(f"✅ 已创建Chrome修复快捷方式: {shortcut_path}")
        return True
        
    except ImportError:
        print("⚠️ 需要安装依赖: pip install winshell pywin32")
        return False
    except Exception as e:
        print(f"❌ 创建快捷方式失败: {e}")
        return False

def main():
    """主函数"""
    print("🍪 B站Cookie管理工具")
    print("=" * 40)
    
    # 1. 检查浏览器进程
    print("1️⃣ 检查浏览器进程...")
    browsers = check_browser_processes()
    
    if browsers:
        for browser, processes in browsers.items():
            if processes:
                print(f"🔍 {browser.title()}: {len(processes)} 个进程运行中")
            else:
                print(f"✅ {browser.title()}: 无运行进程")
    
    # 2. 测试Cookie访问
    print("\n2️⃣ 测试Cookie访问...")
    results = test_browser_cookies()
    
    # 3. 分析结果并给出建议
    print("\n3️⃣ 分析和建议...")
    
    success_browsers = [b for b, r in results.items() if r == "success"]
    locked_browsers = [b for b, r in results.items() if r == "locked"]
    
    if success_browsers:
        print(f"🎉 可用的浏览器: {', '.join(success_browsers)}")
        print("✅ 您可以正常下载B站字幕")
    elif locked_browsers:
        print(f"🔒 被锁定的浏览器: {', '.join(locked_browsers)}")
        print("💡 建议使用Chrome修复方法")
        
        choice = input("\n❓ 是否显示Chrome修复指南? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            show_chrome_fix_guide()
            
            choice2 = input("\n❓ 是否创建Chrome修复快捷方式? (y/N): ").strip().lower()
            if choice2 in ['y', 'yes']:
                create_chrome_shortcut()
    else:
        print("❌ 所有浏览器Cookie访问都失败")
        print("💡 建议:")
        print("  1. 安装并登录Firefox浏览器")
        print("  2. 或手动导出cookies.txt文件")
        print("  3. 或接受弹幕字幕作为替代方案")

if __name__ == "__main__":
    main()
