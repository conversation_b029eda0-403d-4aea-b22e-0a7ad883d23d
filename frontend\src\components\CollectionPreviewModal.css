.collection-preview-modal .ant-modal-content {
  height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #0f0f0f !important;
  border: 1px solid #2d2d2d !important;
}

.collection-preview-modal .ant-modal-header {
  display: none !important;
}

.collection-preview-modal .ant-modal-body {
  padding: 0 !important;
  height: 100%;
}

.collection-preview-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #0f0f0f;
}

/* 头部标题栏 */
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: rgba(26, 26, 26, 0.95);
  border-bottom: 1px solid #2d2d2d;
  backdrop-filter: blur(10px);
}

.preview-header h2 {
  color: #ffffff;
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.preview-header .ant-btn {
  border-radius: 6px;
  font-weight: 400;
  transition: all 0.2s ease;
  height: 32px;
  font-size: 14px;
}

.preview-header .ant-btn-primary {
  background: #4facfe;
  border-color: #4facfe;
  color: white;
}

.preview-header .ant-btn-primary:hover {
  background: #00a8ff;
  border-color: #00a8ff;
  color: white;
}

.preview-header .ant-btn-text {
  color: #cccccc;
  border-color: transparent;
  background: transparent;
}

.preview-header .ant-btn-text:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: transparent;
  color: #ffffff;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 主体内容 */
.preview-content {
  flex: 1;
  overflow: hidden;
}

/* 视频区域 */
.video-section {
  height: 100%;
  background: #000;
  position: relative;
}

.video-player-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-container {
  flex: 1;
  width: 100%;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 视频信息栏 - 固定在视频下方 */
.video-info-bar {
  background: #1a1a1a;
  border-top: 1px solid #2d2d2d;
  padding: 16px 20px;
  min-height: 80px;
}

.video-info-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.video-title-section {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.video-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.video-meta .ant-tag {
  margin: 0;
}

.video-controls {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.video-controls .control-btn {
  border-radius: 6px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #262626;
  border: 1px solid #404040;
  color: #ffffff;
  transition: all 0.2s ease;
  font-size: 14px;
}

.video-controls .control-btn:hover:not(:disabled) {
  background: #4facfe;
  border-color: #4facfe;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.video-controls .control-btn:disabled {
  background: #1a1a1a;
  border-color: #2d2d2d;
  color: #666666;
  cursor: not-allowed;
}

.empty-video {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

/* 播放列表区域 */
.playlist-section {
  height: 100%;
  background: #1a1a1a;
  border-left: 1px solid #2d2d2d;
}

.playlist-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.playlist-header {
  padding: 16px;
  border-bottom: 1px solid #2d2d2d;
  background: #1a1a1a;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.playlist-header .ant-typography {
  color: #ffffff !important;
}

.playlist-header .ant-typography-caption {
  color: #999999 !important;
}

.clips-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

/* 切片项样式 */
.clip-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: #1a1a1a;
  border: 1px solid #2d2d2d;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
}

.clip-item:active {
  cursor: grabbing;
}

.clip-item:hover {
  border-color: #4facfe;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.1);
  background: #262626;
}

.clip-item.active {
  border-color: #4facfe;
  background: rgba(79, 172, 254, 0.1);
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.2);
}

.clip-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #4facfe;
  border-radius: 0 2px 2px 0;
}



.clip-drag-handle {
  color: #999;
  cursor: grab;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  min-height: 24px;
  user-select: none;
}

.clip-drag-handle:hover {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.clip-drag-handle:active {
  cursor: grabbing;
}

/* 确保拖拽时不被其他元素阻挡 */
.clip-item.dragging {
  transform: rotate(2deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  pointer-events: none;
}

.clip-item.dragging * {
  pointer-events: none;
}

.clip-content {
  flex: 1;
  min-width: 0;
}

.clip-title {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 4px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.clip-meta {
  margin-bottom: 6px;
}

.clip-reason {
  font-size: 12px;
  color: #999999;
  margin-top: 4px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.clip-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.clip-item:hover .clip-actions {
  opacity: 1;
}

.clip-actions .ant-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 滚动条样式 */
.clips-list::-webkit-scrollbar {
  width: 6px;
}

.clips-list::-webkit-scrollbar-track {
  background: #1a1a1a;
  border-radius: 3px;
}

.clips-list::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 3px;
}

.clips-list::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .collection-preview-modal .ant-modal {
    width: 95vw !important;
  }
  
  .video-info-overlay {
    top: 8px;
    left: 8px;
    padding: 8px 12px;
  }
  
  .video-title {
    font-size: 14px;
    max-width: 300px;
  }
  
  .video-controls {
    bottom: 60px;
    padding: 8px 16px;
  }
}

@media (max-width: 768px) {
  .preview-content .ant-row {
    flex-direction: column;
  }
  
  .video-section {
    height: 60vh;
  }
  
  .playlist-section {
    height: 40vh;
    border-left: none;
    border-top: 1px solid #e8e8e8;
  }
  
  .clip-item {
    padding: 8px;
  }
  
  .clip-actions {
    opacity: 1;
  }
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.video-section {
  animation: slideInLeft 0.3s ease-out;
}

.playlist-section {
  animation: slideInRight 0.3s ease-out;
}

/* 加载状态 */
.clip-item .ant-btn-loading {
  pointer-events: none;
}

/* 拖拽时的占位符样式 */
.clips-list .ant-empty {
  margin: 0;
  padding: 20px;
}

/* 确保模态框在最顶层 */
.collection-preview-modal {
  z-index: 1050;
}

/* 播放按钮悬停效果 */
.video-controls .ant-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: transparent;
}

/* 标签样式优化 */
.video-meta .ant-tag {
  margin: 0;
  border: none;
  font-size: 11px;
  padding: 2px 6px;
}

/* 头部按钮样式 */
.header-right .ant-btn {
  border-color: rgba(255, 255, 255, 0.3);
}

.header-right .ant-btn:hover {
  border-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
}

/* 拖拽占位符 */
.drag-placeholder {
  height: 80px;
  border: 2px dashed #404040;
  border-radius: 8px;
  margin: 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999999;
  font-size: 14px;
  background: #262626;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999999;
}