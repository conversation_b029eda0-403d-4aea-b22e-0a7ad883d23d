# 商业财经视频时间点定位 Prompt

## 核心原则：精准、完整、商业逻辑清晰

1.  **精准定位**：时间戳必须严格对应商业话题相关的核心讨论内容。
2.  **完整性**：时间范围必须完整覆盖一个商业话题的所有关键论证点，确保商业逻辑完整。
3.  **自然边界**：优先在商业观点转换、案例切换或逻辑段落边界处定位时间点。

---

## 关键指令：如何确定 `start_time` 和 `end_time`

### `start_time` 的确定：
-   应定位到讨论该商业话题的**第一句核心观点**的开始时间。
-   忽略该观点之前的无关寒暄、免责声明或过渡语。
-   **优先选择**：在商业逻辑边界处开始，确保话题引入自然且逻辑清晰。
-   **商业内容特殊考虑**：如果有重要的背景铺垫或数据引用，应适当前移起始时间。

### `end_time` 的确定 (最重要)：
-   **必须**是覆盖该商业话题核心讨论的**最后一句话的结束时间戳**。
-   **商业逻辑完整性**：确保商业分析、投资建议、风险提示等核心内容完整，不出现逻辑断层。
-   **自然结束**：优先选择在总结性语句、投资建议结论或话题转换处结束。
-   **风险提示包含**：如果话题末尾有重要的风险提示或免责声明，应包含在内。
-   如果商业话题的讨论一直持续到所提供SRT文本块的末尾，那么 `end_time` 就应该是最后一句相关字幕的结束时间戳。
-   **错误做法**：将 `end_time` 无脑设置为整个文本块的结束时间。这是绝对要避免的。

### 商业内容时长控制原则：
-   **目标时长**：每个商业话题片段应在3-8分钟之间
-   **复杂分析时长**：涉及多维度分析的复杂商业话题可适当延长至10分钟
-   **快讯类内容**：市场快讯、政策解读等可压缩至2-3分钟
-   **深度分析**：行业研究、公司分析等深度内容可延长至12分钟

### 商业内容特殊处理：
-   **数据密集段落**：包含大量财务数据、市场数据的段落应完整保留
-   **案例分析**：完整的商业案例分析应作为一个整体，避免拆分
-   **投资建议**：投资策略和风险提示应配套出现，不可分离
-   **政策解读**：政策内容与影响分析应保持完整性

---

## 输入格式
你将收到一个JSON对象，包含：
-   `outline`: 一个包含**多个**待处理商业话题的JSON数组。
-   `srt_text`: 与这批话题相关的**单个**SRT文本块，格式为 `序号\n开始 --> 结束\n文本\n\n`。

## 输出格式
-   严格按照输入大纲的结构，为**每个**话题对象补充 `start_time` 和 `end_time` 字段。
-   **关键：** 在输出时，请将输入的 `title` 字段重命名为 `outline`，并将 `subtopics` 字段重命名为 `content`。
-   最终输出一个包含**所有**处理后话题的JSON数组。
-   确保时间格式为 `HH:MM:SS,mmm`。

**严格的JSON输出要求：**
1. 输出必须以 `[` 开始，以 `]` 结束，不要添加任何解释文字、标题或Markdown代码块标记
2. 使用标准英文双引号 "，绝不使用中文引号 "" 或单引号
3. 确保所有括号、方括号正确匹配，对象间用逗号分隔，最后一个对象后不加逗号
4. 字符串中的引号必须转义为 \"
5. 不能包含任何注释、额外文本或控制字符
6. 确保JSON格式完全有效，可以被标准JSON解析器解析

## 商业内容时间定位示例

### 正确的时间定位：
- **投资策略讨论**：从"我认为当前市场..."开始，到"这就是我的投资建议"结束
- **公司分析**：从"我们来看这家公司..."开始，到"综合评估结果"结束
- **行业趋势**：从"这个行业的发展趋势..."开始，到"未来展望"结束

### 需要避免的错误：
- 在财务数据讲解中间断开
- 将投资建议与风险提示分离
- 在案例分析的关键论证点处截断
- 忽略重要的市场背景信息

## 注意事项

1. **商业逻辑完整性**：确保每个片段包含完整的商业分析逻辑
2. **数据准确性**：重要的财务数据、市场数据必须完整包含
3. **风险平衡**：投资建议与风险提示应配套出现
4. **时效性考虑**：市场快讯类内容应紧凑处理，深度分析可适当延长
5. **受众适配**：考虑不同投资经验受众的理解需求