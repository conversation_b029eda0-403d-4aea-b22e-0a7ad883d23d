# 演讲脱口秀视频时间点定位 Prompt

## 核心原则：表达完整、情感连贯、效果保持

1.  **表达完整性**：时间戳必须严格对应完整的表达单元和内容段落。
2.  **情感连贯性**：时间范围必须完整覆盖情感表达的起承转合，确保情感逻辑完整。
3.  **自然边界**：优先在话题转换、情感转换或表达节奏的自然边界处定位时间点。

---

## 关键指令：如何确定 `start_time` 和 `end_time`

### `start_time` 的确定：
-   应定位到该话题表达的**第一句核心内容**的开始时间。
-   忽略该内容之前的无关过渡、停顿或背景音乐。
-   **优先选择**：在话题明确提出或情感表达开始的起始点，确保表达的完整性。
-   **演讲脱口秀特殊考虑**：如果有重要的铺垫、引入或氛围营造，应适当前移起始时间。

### `end_time` 的确定 (最重要)：
-   **必须**是覆盖该话题核心表达的**最后一句话的结束时间戳**。
-   **表达完整性**：确保观点阐述、故事讲述、笑点呈现等核心内容完整，不出现表达断层。
-   **自然结束**：优先选择在总结性语句、情感高潮后或话题转换处结束。
-   **效果保持**：如果话题末尾有重要的观众反应、笑声或掌声，应包含在内。
-   如果话题表达的讨论一直持续到所提供SRT文本块的末尾，那么 `end_time` 就应该是最后一句相关字幕的结束时间戳。
-   **错误做法**：将 `end_time` 无脑设置为整个文本块的结束时间。这是绝对要避免的。

### 演讲脱口秀内容时长控制原则：
-   **目标时长**：每个话题片段应在3-8分钟之间
-   **深度演讲时长**：涉及深度思考或复杂论述的演讲可适当延长至10分钟
-   **精彩段子**：独立完整的段子或笑点可压缩至2-3分钟
-   **重要演讲**：核心主题演讲或重要观点阐述可延长至12分钟

### 演讲脱口秀内容特殊处理：
-   **完整段子**：包含铺垫、发展、笑点的完整段子应作为一个整体
-   **情感表达**：重要的情感渲染和氛围营造应完整保留
-   **观众互动**：与观众的互动、问答、现场反应应配套出现
-   **观点论述**：核心观点的提出、论证、总结应完整包含

---

## 输入格式
你将收到一个JSON对象，包含：
-   `outline`: 一个包含**多个**待处理演讲话题的JSON数组。
-   `srt_text`: 与这批话题相关的**单个**SRT文本块，格式为 `序号\n开始 --> 结束\n文本\n\n`。

## 输出格式
-   严格按照输入大纲的结构，为**每个**话题对象补充 `start_time` 和 `end_time` 字段。
-   **关键：** 在输出时，请将输入的 `title` 字段重命名为 `outline`，并将 `subtopics` 字段重命名为 `content`。
-   最终输出一个包含**所有**处理后话题的JSON数组。
-   确保时间格式为 `HH:MM:SS,mmm`。

**严格的JSON输出要求：**
1. 输出必须以 `[` 开始，以 `]` 结束，不要添加任何解释文字、标题或Markdown代码块标记
2. 使用标准英文双引号 "，绝不使用中文引号 "" 或单引号
3. 确保所有括号、方括号正确匹配，对象间用逗号分隔，最后一个对象后不加逗号
4. 字符串中的引号必须转义为 \"
5. 不能包含任何注释、额外文本或控制字符
6. 确保JSON格式完全有效，可以被标准JSON解析器解析

## 演讲脱口秀内容时间定位示例

### 正确的时间定位：
- **演讲观点**：从"我想跟大家分享一个观点..."开始，到"这就是我的看法"结束
- **脱口秀段子**：从段子的铺垫开始，到笑点结束和观众反应结束
- **故事分享**：从"我给大家讲个故事..."开始，到故事结论和感悟结束
- **现场互动**：从互动发起开始，到互动结束和回应总结

### 需要避免的错误：
- 在段子的笑点处断开
- 将观点阐述与论证过程分离
- 在情感表达的高潮处截断
- 忽略重要的观众反应或现场效果

## 演讲脱口秀类内容的特殊考虑

### 1. 演讲类内容：
- 确保观点的完整阐述和论证过程
- 保留重要的情感表达和感染力内容
- 包含与观众的互动和现场反应
- 确保启发性内容的完整传达

### 2. 脱口秀类内容：
- 保持段子的完整性和幽默效果
- 包含铺垫、发展、笑点的完整结构
- 保留观众的笑声和现场反应
- 确保幽默技巧的完整展示

### 3. 访谈类内容：
- 包含问题提出与回答的完整过程
- 保持对话的自然流畅和逻辑性
- 确保深度交流的完整性
- 包含重要的观点碰撞和思想交流

### 4. 现场表演类：
- 保留即兴表演的完整性和自然性
- 包含与观众的实时互动和反馈
- 确保表演技巧的完整展示
- 保持现场氛围和情感感染力

## 不同演讲脱口秀类型的时间定位策略

### 主题演讲：
- 按观点层次定位：核心观点-支撑论据-总结升华
- 保持论证逻辑的完整性
- 包含重要的情感渲染和感染力内容
- 确保启发性和教育性的完整传达

### 脱口秀表演：
- 按段子单元定位：独立段子-连续笑点-主题串联
- 保持幽默效果的完整性
- 包含观众反应和现场效果
- 确保娱乐价值的最大化

### 访谈对话：
- 按话题讨论定位：问题提出-深度交流-观点总结
- 保持对话的自然流畅
- 包含重要的观点碰撞和思想交流
- 确保信息传递的完整性

### 经验分享：
- 按经历阶段定位：背景介绍-过程描述-感悟总结
- 保持故事的完整性和感染力
- 包含重要的人生感悟和启发
- 确保经验价值的完整传递

## 注意事项

1. **表达完整性**：确保每个片段包含完整的表达逻辑和内容结构
2. **情感连贯性**：重要的情感表达和氛围营造必须完整包含
3. **效果保持**：笑点、掌声、观众反应等现场效果应作为重要内容
4. **互动价值**：精彩的现场互动和观众参与应完整保留
5. **启发价值**：对于演讲内容，确保启发性和教育性的完整传达
6. **娱乐效果**：对于脱口秀内容，确保幽默效果和娱乐价值的完整性
7. **真实自然**：保持内容的真实性和现场感，避免过度剪辑