{"version": 3, "sources": ["../../react-player/lib/players/Twitch.js"], "sourcesContent": ["var __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nvar Twitch_exports = {};\n__export(Twitch_exports, {\n  default: () => Twitch\n});\nmodule.exports = __toCommonJS(Twitch_exports);\nvar import_react = __toESM(require(\"react\"));\nvar import_utils = require(\"../utils\");\nvar import_patterns = require(\"../patterns\");\nconst SDK_URL = \"https://player.twitch.tv/js/embed/v1.js\";\nconst SDK_GLOBAL = \"Twitch\";\nconst PLAYER_ID_PREFIX = \"twitch-player-\";\nclass Twitch extends import_react.Component {\n  constructor() {\n    super(...arguments);\n    __publicField(this, \"callPlayer\", import_utils.callPlayer);\n    __publicField(this, \"playerID\", this.props.config.playerId || `${PLAYER_ID_PREFIX}${(0, import_utils.randomString)()}`);\n    __publicField(this, \"mute\", () => {\n      this.callPlayer(\"setMuted\", true);\n    });\n    __publicField(this, \"unmute\", () => {\n      this.callPlayer(\"setMuted\", false);\n    });\n  }\n  componentDidMount() {\n    this.props.onMount && this.props.onMount(this);\n  }\n  load(url, isReady) {\n    const { playsinline, onError, config, controls } = this.props;\n    const isChannel = import_patterns.MATCH_URL_TWITCH_CHANNEL.test(url);\n    const id = isChannel ? url.match(import_patterns.MATCH_URL_TWITCH_CHANNEL)[1] : url.match(import_patterns.MATCH_URL_TWITCH_VIDEO)[1];\n    if (isReady) {\n      if (isChannel) {\n        this.player.setChannel(id);\n      } else {\n        this.player.setVideo(\"v\" + id);\n      }\n      return;\n    }\n    (0, import_utils.getSDK)(SDK_URL, SDK_GLOBAL).then((Twitch2) => {\n      this.player = new Twitch2.Player(this.playerID, {\n        video: isChannel ? \"\" : id,\n        channel: isChannel ? id : \"\",\n        height: \"100%\",\n        width: \"100%\",\n        playsinline,\n        autoplay: this.props.playing,\n        muted: this.props.muted,\n        // https://github.com/CookPete/react-player/issues/733#issuecomment-549085859\n        controls: isChannel ? true : controls,\n        time: (0, import_utils.parseStartTime)(url),\n        ...config.options\n      });\n      const { READY, PLAYING, PAUSE, ENDED, ONLINE, OFFLINE, SEEK } = Twitch2.Player;\n      this.player.addEventListener(READY, this.props.onReady);\n      this.player.addEventListener(PLAYING, this.props.onPlay);\n      this.player.addEventListener(PAUSE, this.props.onPause);\n      this.player.addEventListener(ENDED, this.props.onEnded);\n      this.player.addEventListener(SEEK, this.props.onSeek);\n      this.player.addEventListener(ONLINE, this.props.onLoaded);\n      this.player.addEventListener(OFFLINE, this.props.onLoaded);\n    }, onError);\n  }\n  play() {\n    this.callPlayer(\"play\");\n  }\n  pause() {\n    this.callPlayer(\"pause\");\n  }\n  stop() {\n    this.callPlayer(\"pause\");\n  }\n  seekTo(seconds, keepPlaying = true) {\n    this.callPlayer(\"seek\", seconds);\n    if (!keepPlaying) {\n      this.pause();\n    }\n  }\n  setVolume(fraction) {\n    this.callPlayer(\"setVolume\", fraction);\n  }\n  getDuration() {\n    return this.callPlayer(\"getDuration\");\n  }\n  getCurrentTime() {\n    return this.callPlayer(\"getCurrentTime\");\n  }\n  getSecondsLoaded() {\n    return null;\n  }\n  render() {\n    const style = {\n      width: \"100%\",\n      height: \"100%\"\n    };\n    return /* @__PURE__ */ import_react.default.createElement(\"div\", { style, id: this.playerID });\n  }\n}\n__publicField(Twitch, \"displayName\", \"Twitch\");\n__publicField(Twitch, \"canPlay\", import_patterns.canPlay.twitch);\n__publicField(Twitch, \"loopOnEnded\", true);\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW,OAAO;AACtB,QAAI,YAAY,OAAO;AACvB,QAAI,mBAAmB,OAAO;AAC9B,QAAI,oBAAoB,OAAO;AAC/B,QAAI,eAAe,OAAO;AAC1B,QAAI,eAAe,OAAO,UAAU;AACpC,QAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,QAAI,WAAW,CAAC,QAAQ,QAAQ;AAC9B,eAAS,QAAQ;AACf,kBAAU,QAAQ,MAAM,EAAE,KAAK,IAAI,IAAI,GAAG,YAAY,KAAK,CAAC;AAAA,IAChE;AACA,QAAI,cAAc,CAAC,IAAI,MAAM,QAAQ,SAAS;AAC5C,UAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;AAClE,iBAAS,OAAO,kBAAkB,IAAI;AACpC,cAAI,CAAC,aAAa,KAAK,IAAI,GAAG,KAAK,QAAQ;AACzC,sBAAU,IAAI,KAAK,EAAE,KAAK,MAAM,KAAK,GAAG,GAAG,YAAY,EAAE,OAAO,iBAAiB,MAAM,GAAG,MAAM,KAAK,WAAW,CAAC;AAAA,MACvH;AACA,aAAO;AAAA,IACT;AACA,QAAI,UAAU,CAAC,KAAK,YAAY,YAAY,SAAS,OAAO,OAAO,SAAS,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,MAKnG,cAAc,CAAC,OAAO,CAAC,IAAI,aAAa,UAAU,QAAQ,WAAW,EAAE,OAAO,KAAK,YAAY,KAAK,CAAC,IAAI;AAAA,MACzG;AAAA,IACF;AACA,QAAI,eAAe,CAAC,QAAQ,YAAY,UAAU,CAAC,GAAG,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,GAAG;AACzF,QAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU;AACvC,sBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AACpE,aAAO;AAAA,IACT;AACA,QAAI,iBAAiB,CAAC;AACtB,aAAS,gBAAgB;AAAA,MACvB,SAAS,MAAM;AAAA,IACjB,CAAC;AACD,WAAO,UAAU,aAAa,cAAc;AAC5C,QAAI,eAAe,QAAQ,eAAgB;AAC3C,QAAI,eAAe;AACnB,QAAI,kBAAkB;AACtB,QAAM,UAAU;AAChB,QAAM,aAAa;AACnB,QAAM,mBAAmB;AACzB,QAAM,SAAN,cAAqB,aAAa,UAAU;AAAA,MAC1C,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,sBAAc,MAAM,cAAc,aAAa,UAAU;AACzD,sBAAc,MAAM,YAAY,KAAK,MAAM,OAAO,YAAY,GAAG,gBAAgB,IAAI,GAAG,aAAa,cAAc,CAAC,EAAE;AACtH,sBAAc,MAAM,QAAQ,MAAM;AAChC,eAAK,WAAW,YAAY,IAAI;AAAA,QAClC,CAAC;AACD,sBAAc,MAAM,UAAU,MAAM;AAClC,eAAK,WAAW,YAAY,KAAK;AAAA,QACnC,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,MAAM,WAAW,KAAK,MAAM,QAAQ,IAAI;AAAA,MAC/C;AAAA,MACA,KAAK,KAAK,SAAS;AACjB,cAAM,EAAE,aAAa,SAAS,QAAQ,SAAS,IAAI,KAAK;AACxD,cAAM,YAAY,gBAAgB,yBAAyB,KAAK,GAAG;AACnE,cAAM,KAAK,YAAY,IAAI,MAAM,gBAAgB,wBAAwB,EAAE,CAAC,IAAI,IAAI,MAAM,gBAAgB,sBAAsB,EAAE,CAAC;AACnI,YAAI,SAAS;AACX,cAAI,WAAW;AACb,iBAAK,OAAO,WAAW,EAAE;AAAA,UAC3B,OAAO;AACL,iBAAK,OAAO,SAAS,MAAM,EAAE;AAAA,UAC/B;AACA;AAAA,QACF;AACA,SAAC,GAAG,aAAa,QAAQ,SAAS,UAAU,EAAE,KAAK,CAAC,YAAY;AAC9D,eAAK,SAAS,IAAI,QAAQ,OAAO,KAAK,UAAU;AAAA,YAC9C,OAAO,YAAY,KAAK;AAAA,YACxB,SAAS,YAAY,KAAK;AAAA,YAC1B,QAAQ;AAAA,YACR,OAAO;AAAA,YACP;AAAA,YACA,UAAU,KAAK,MAAM;AAAA,YACrB,OAAO,KAAK,MAAM;AAAA;AAAA,YAElB,UAAU,YAAY,OAAO;AAAA,YAC7B,OAAO,GAAG,aAAa,gBAAgB,GAAG;AAAA,YAC1C,GAAG,OAAO;AAAA,UACZ,CAAC;AACD,gBAAM,EAAE,OAAO,SAAS,OAAO,OAAO,QAAQ,SAAS,KAAK,IAAI,QAAQ;AACxE,eAAK,OAAO,iBAAiB,OAAO,KAAK,MAAM,OAAO;AACtD,eAAK,OAAO,iBAAiB,SAAS,KAAK,MAAM,MAAM;AACvD,eAAK,OAAO,iBAAiB,OAAO,KAAK,MAAM,OAAO;AACtD,eAAK,OAAO,iBAAiB,OAAO,KAAK,MAAM,OAAO;AACtD,eAAK,OAAO,iBAAiB,MAAM,KAAK,MAAM,MAAM;AACpD,eAAK,OAAO,iBAAiB,QAAQ,KAAK,MAAM,QAAQ;AACxD,eAAK,OAAO,iBAAiB,SAAS,KAAK,MAAM,QAAQ;AAAA,QAC3D,GAAG,OAAO;AAAA,MACZ;AAAA,MACA,OAAO;AACL,aAAK,WAAW,MAAM;AAAA,MACxB;AAAA,MACA,QAAQ;AACN,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO;AACL,aAAK,WAAW,OAAO;AAAA,MACzB;AAAA,MACA,OAAO,SAAS,cAAc,MAAM;AAClC,aAAK,WAAW,QAAQ,OAAO;AAC/B,YAAI,CAAC,aAAa;AAChB,eAAK,MAAM;AAAA,QACb;AAAA,MACF;AAAA,MACA,UAAU,UAAU;AAClB,aAAK,WAAW,aAAa,QAAQ;AAAA,MACvC;AAAA,MACA,cAAc;AACZ,eAAO,KAAK,WAAW,aAAa;AAAA,MACtC;AAAA,MACA,iBAAiB;AACf,eAAO,KAAK,WAAW,gBAAgB;AAAA,MACzC;AAAA,MACA,mBAAmB;AACjB,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,QAAQ;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,QACV;AACA,eAAuB,aAAa,QAAQ,cAAc,OAAO,EAAE,OAAO,IAAI,KAAK,SAAS,CAAC;AAAA,MAC/F;AAAA,IACF;AACA,kBAAc,QAAQ,eAAe,QAAQ;AAC7C,kBAAc,QAAQ,WAAW,gBAAgB,QAAQ,MAAM;AAC/D,kBAAc,QAAQ,eAAe,IAAI;AAAA;AAAA;", "names": []}