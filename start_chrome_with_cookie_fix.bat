@echo off
echo 🔧 启动Chrome (Cookie修复模式)
echo ================================

REM 查找Chrome安装路径
set CHROME_PATH=""
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files\Google\Chrome\Application\chrome.exe"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
) else if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    set CHROME_PATH="%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"
)

if %CHROME_PATH%=="" (
    echo ❌ 未找到Chrome安装路径
    echo 请手动安装Chrome浏览器
    pause
    exit /b 1
)

echo ✅ 找到Chrome: %CHROME_PATH%
echo 🚀 启动Chrome (禁用Cookie数据库锁定)...

REM 启动Chrome并禁用Cookie数据库锁定
start "" %CHROME_PATH% --disable-features=LockProfileCookieDatabase

echo ✅ Chrome已启动
echo 💡 现在可以正常使用B站视频下载功能
echo 📝 请在Chrome中登录bilibili.com以获得最佳体验
pause
