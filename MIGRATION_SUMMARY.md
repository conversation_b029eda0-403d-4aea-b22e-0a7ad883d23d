# API迁移总结：从阿里云通义千问到魔搭平台

## 🎯 迁移目标
将项目从阿里云通义千问API迁移到魔搭平台的OpenAI格式API，以利用每天2000次的免费额度。

## 📋 修改清单

### 1. 依赖包更新
- **文件**: `requirements.txt`
- **修改**: 
  - 移除: `dashscope==1.23.5`
  - 添加: `openai>=1.0.0`

### 2. 配置文件更新
- **文件**: `src/config.py`
- **主要修改**:
  - `DASHSCOPE_API_KEY` → `OPENAI_API_KEY`
  - `OPENAI_BASE_URL` 新增环境变量支持
  - 默认模型名称: `qwen-plus` → `Qwen/Qwen2.5-Coder-32B-Instruct`
  - 默认base_url: `https://dashscope.aliyuncs.com` → `https://api-inference.modelscope.cn/v1/`

### 3. LLM客户端重构
- **文件**: `src/utils/llm_client.py`
- **主要修改**:
  - 导入: `from dashscope import Generation` → `from openai import OpenAI`
  - API调用格式: 从dashscope格式改为OpenAI Chat Completions格式
  - 响应处理: 适配OpenAI响应结构
  - 错误处理: 更新异常类型和错误信息

### 4. API密钥管理器更新
- **文件**: `src/utils/api_key_manager.py`
- **修改**: 默认provider从 `dashscope` 改为 `openai`

### 5. 测试文件更新
- **文件**: `tests/test_config.py`
- **修改**: 更新所有测试用例中的期望值，匹配新的配置

### 6. 配置示例更新
- **文件**: `data/settings.example.json`
- **新格式**:
```json
{
  "openai_api_key": "MODELSCOPE_ACCESS_TOKEN",
  "openai_base_url": "https://api-inference.modelscope.cn/v1/",
  "model_name": "Qwen/Qwen2.5-Coder-32B-Instruct",
  "chunk_size": 5000,
  "min_score_threshold": 0.7,
  "max_clips_per_collection": 5,
  "default_browser": null
}
```

### 7. 文档更新
- **文件**: `.github/README.md`
- **修改**: 更新API密钥配置说明，添加魔搭平台获取指南

## 🔧 配置方法

### 环境变量方式
```bash
export OPENAI_API_KEY="your_modelscope_access_token"
export OPENAI_BASE_URL="https://api-inference.modelscope.cn/v1/"
```

### 配置文件方式
编辑 `data/settings.json`:
```json
{
  "openai_api_key": "your_modelscope_access_token",
  "openai_base_url": "https://api-inference.modelscope.cn/v1/",
  "model_name": "Qwen/Qwen2.5-Coder-32B-Instruct"
}
```

## 🚀 获取魔搭平台API密钥

1. 访问 [魔搭社区](https://modelscope.cn/)
2. 注册并登录账号
3. 进入个人中心获取Access Token
4. 每天有2000次免费调用额度

## ✅ 验证步骤

1. **安装依赖**:
   ```bash
   pip install openai>=1.0.0
   ```

2. **配置API密钥**:
   - 设置环境变量或编辑配置文件

3. **运行测试**:
   ```bash
   python -m pytest tests/test_config.py -v
   python test_llm_client.py
   ```

## 🎉 迁移优势

1. **成本降低**: 每天2000次免费调用
2. **兼容性好**: 使用标准OpenAI格式API
3. **易于扩展**: 可轻松切换到其他OpenAI兼容的服务
4. **向后兼容**: 保持相同的接口，只改内部实现

## ⚠️ 注意事项

1. **API密钥格式**: 魔搭平台使用Access Token，不是传统的API Key格式
2. **模型名称**: 使用完整的模型路径，如 `Qwen/Qwen2.5-Coder-32B-Instruct`
3. **请求格式**: 从单一prompt改为messages格式
4. **错误处理**: 更新了异常类型和错误信息

## 🔄 回滚方案

如需回滚到原来的dashscope API：
1. 恢复 `requirements.txt` 中的 `dashscope==1.23.5`
2. 恢复 `src/utils/llm_client.py` 的原始实现
3. 恢复配置文件中的字段名称

迁移已完成，所有测试通过！🎊
