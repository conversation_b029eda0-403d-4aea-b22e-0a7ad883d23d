# B站视频下载问题排查指南

## 🚨 常见问题：Chrome Cookie 数据库锁定

### 问题描述
下载B站视频时出现错误：
```
ERROR: Could not copy Chrome cookie database
```

### 原因分析
这是Windows版Chrome和其他基于Chromium的浏览器（如Edge）的已知问题。当Chrome正在运行时，其Cookie数据库被锁定，yt-dlp无法访问。

## 🛠️ 解决方案

### 方案1：关闭Chrome浏览器（推荐）
1. **完全关闭Chrome浏览器**
   - 点击Chrome右上角的 ❌ 关闭所有窗口
   - 确保系统托盘中没有Chrome图标
   
2. **确认所有Chrome进程已退出**
   - 按 `Ctrl + Shift + Esc` 打开任务管理器
   - 查找并结束所有 `chrome.exe` 进程
   
3. **重新运行下载任务**

### 方案2：使用Chrome启动参数
1. **找到Chrome桌面快捷方式**
2. **右键点击 → 属性**
3. **在"目标"字段末尾添加参数**：
   ```
   --disable-features=LockProfileCookieDatabase
   ```
   
   完整示例：
   ```
   "C:\Program Files\Google\Chrome\Application\chrome.exe" --disable-features=LockProfileCookieDatabase
   ```

4. **点击确定保存**
5. **使用修改后的快捷方式启动Chrome**

### 方案3：使用其他浏览器
在下载设置中选择其他浏览器：
- **Firefox**：通常没有Cookie锁定问题
- **Edge**：可能有类似问题，但值得尝试
- **无浏览器模式**：不使用Cookie，可能无法下载需要登录的视频

### 方案4：安装Chrome插件
安装 `ChromeCookieUnlock` yt-dlp 插件：
- 参考：https://github.com/yt-dlp/yt-dlp/issues/7271

## 🔧 自动修复工具

项目提供了自动修复工具：

```bash
python chrome_cookie_fix.py
```

该工具会：
1. 检测运行中的Chrome进程
2. 提供终止Chrome进程的选项
3. 测试yt-dlp Cookie访问
4. 提供详细的修复建议

## 📋 故障排查步骤

### 1. 检查Chrome进程
```bash
# 使用修复工具检查
python chrome_cookie_fix.py

# 或手动检查任务管理器
# 查找所有 chrome.exe 进程
```

### 2. 测试下载
```bash
# 测试简单下载（无Cookie）
yt-dlp --simulate "https://www.bilibili.com/video/BV12v4y1y7uV"

# 测试Chrome Cookie访问
yt-dlp --cookies-from-browser chrome --simulate "https://www.bilibili.com/video/BV12v4y1y7uV"
```

### 3. 查看详细日志
在项目中，下载失败时会显示详细的错误信息和建议。

## ⚡ 快速解决方案

**最快的解决方法**：
1. 关闭所有Chrome窗口
2. 等待5秒
3. 重新尝试下载

**如果仍然失败**：
1. 打开任务管理器
2. 结束所有 `chrome.exe` 进程
3. 重新尝试下载

## 🔍 技术细节

### 为什么会出现这个问题？
- Chrome使用SQLite数据库存储Cookie
- 当Chrome运行时，数据库文件被锁定
- yt-dlp需要读取Cookie来访问需要登录的内容
- Windows文件锁定机制阻止了并发访问

### 项目中的改进
我们已经在代码中添加了：
1. **自动浏览器检测**：尝试多个浏览器
2. **备用下载方法**：无Cookie模式
3. **详细错误提示**：用户友好的错误信息
4. **进程检测**：自动检测Chrome进程状态

## 📞 获取帮助

如果以上方案都无法解决问题：
1. 运行 `python chrome_cookie_fix.py` 获取详细诊断
2. 查看项目日志中的详细错误信息
3. 尝试使用Firefox浏览器
4. 考虑使用无Cookie模式下载公开视频

---

**注意**：某些需要登录才能观看的视频，在无Cookie模式下可能无法下载。
