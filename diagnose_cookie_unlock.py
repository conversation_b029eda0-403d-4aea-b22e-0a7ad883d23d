#!/usr/bin/env python3
"""
诊断Cookie解锁工具状态
"""
import subprocess
import sys
import os
from pathlib import Path

def check_yt_dlp_plugins():
    """检查yt-dlp插件"""
    print("🔍 检查yt-dlp插件...")
    
    try:
        result = subprocess.run(["yt-dlp", "--help"], capture_output=True, text=True)
        if "chrome-cookie-unlock" in result.stdout.lower():
            print("✅ 检测到Chrome Cookie Unlock插件")
            return True
        else:
            print("❌ 未检测到Chrome Cookie Unlock插件")
            return False
    except Exception as e:
        print(f"❌ 检查插件失败: {e}")
        return False

def check_chrome_processes():
    """检查Chrome进程"""
    print("\n🔍 检查Chrome进程...")
    
    try:
        result = subprocess.run(["tasklist", "/fi", "imagename eq chrome.exe"], 
                              capture_output=True, text=True)
        if "chrome.exe" in result.stdout:
            lines = [line for line in result.stdout.split('\n') if 'chrome.exe' in line]
            print(f"🔍 发现 {len(lines)} 个Chrome进程")
            for line in lines[:3]:  # 只显示前3个
                print(f"   {line.strip()}")
            return True
        else:
            print("✅ 没有Chrome进程运行")
            return False
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return False

def test_cookie_access_methods():
    """测试多种Cookie访问方法"""
    print("\n🧪 测试Cookie访问方法...")
    
    test_url = "https://www.bilibili.com/video/BV12v4y1y7uV"
    methods = [
        {
            "name": "Chrome Cookie",
            "cmd": ["yt-dlp", "--cookies-from-browser", "chrome", "--simulate", "--print", "title", test_url]
        },
        {
            "name": "Firefox Cookie", 
            "cmd": ["yt-dlp", "--cookies-from-browser", "firefox", "--simulate", "--print", "title", test_url]
        },
        {
            "name": "Edge Cookie",
            "cmd": ["yt-dlp", "--cookies-from-browser", "edge", "--simulate", "--print", "title", test_url]
        }
    ]
    
    results = {}
    
    for method in methods:
        print(f"\n🔧 测试: {method['name']}")
        try:
            result = subprocess.run(method['cmd'], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ {method['name']} 成功")
                # 提取视频标题
                lines = result.stdout.strip().split('\n')
                title = lines[-1] if lines else "未知标题"
                print(f"📺 视频标题: {title}")
                results[method['name']] = "success"
            else:
                error = result.stderr.lower()
                if "could not copy" in error and "cookie database" in error:
                    print(f"🔒 {method['name']} - Cookie数据库被锁定")
                    results[method['name']] = "locked"
                elif "failed to decrypt" in error:
                    print(f"🔐 {method['name']} - DPAPI解密失败")
                    results[method['name']] = "decrypt_failed"
                elif "could not find" in error:
                    print(f"❌ {method['name']} - 浏览器未安装")
                    results[method['name']] = "not_found"
                else:
                    print(f"❌ {method['name']} - 其他错误")
                    print(f"   错误: {result.stderr[:100]}")
                    results[method['name']] = "other_error"
                    
        except subprocess.TimeoutExpired:
            print(f"⏰ {method['name']} - 测试超时")
            results[method['name']] = "timeout"
        except Exception as e:
            print(f"❌ {method['name']} - 异常: {e}")
            results[method['name']] = "exception"
    
    return results

def check_cookie_files():
    """检查Cookie文件"""
    print("\n📁 检查Cookie文件...")
    
    cookie_paths = [
        Path("cookies.txt"),
        Path.home() / "cookies.txt",
        Path("data/cookies.txt")
    ]
    
    found_cookies = []
    for path in cookie_paths:
        if path.exists():
            size = path.stat().st_size
            print(f"✅ 找到Cookie文件: {path} ({size} bytes)")
            found_cookies.append(path)
        else:
            print(f"❌ 未找到: {path}")
    
    return found_cookies

def test_subtitle_download():
    """测试字幕下载"""
    print("\n📝 测试字幕下载...")
    
    test_url = "https://www.bilibili.com/video/BV12v4y1y7uV"
    
    # 测试无Cookie字幕下载
    print("🔧 测试无Cookie字幕下载...")
    cmd = [
        "yt-dlp",
        "--write-auto-sub",
        "--sub-lang", "zh-Hans",
        "--sub-format", "srt", 
        "--skip-download",
        "--output", "test_subtitle.%(ext)s",
        test_url
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        if "There are no subtitles" in result.stdout:
            print("❌ 无Cookie模式无法获取字幕")
            return False
        elif result.returncode == 0:
            print("✅ 无Cookie模式成功获取字幕")
            return True
        else:
            print(f"❌ 字幕下载失败: {result.stderr[:100]}")
            return False
    except Exception as e:
        print(f"❌ 字幕下载异常: {e}")
        return False

def main():
    """主函数"""
    print("🔍 Cookie解锁工具诊断")
    print("=" * 40)
    
    # 1. 检查yt-dlp插件
    has_plugin = check_yt_dlp_plugins()
    
    # 2. 检查Chrome进程
    has_chrome = check_chrome_processes()
    
    # 3. 测试Cookie访问
    cookie_results = test_cookie_access_methods()
    
    # 4. 检查Cookie文件
    cookie_files = check_cookie_files()
    
    # 5. 测试字幕下载
    subtitle_ok = test_subtitle_download()
    
    # 6. 总结和建议
    print("\n📊 诊断总结")
    print("=" * 40)
    
    success_methods = [k for k, v in cookie_results.items() if v == "success"]
    
    if success_methods:
        print(f"🎉 可用的Cookie方法: {', '.join(success_methods)}")
        print("✅ 您可以下载完整字幕")
    elif cookie_files:
        print(f"📁 找到Cookie文件: {len(cookie_files)} 个")
        print("💡 建议使用 --cookies cookies.txt 参数")
    else:
        print("❌ 所有Cookie方法都失败")
        print("💡 建议:")
        if has_chrome:
            print("   1. 关闭所有Chrome进程后重试")
        print("   2. 手动导出cookies.txt文件")
        print("   3. 或接受弹幕字幕作为替代")
    
    if not subtitle_ok:
        print("\n⚠️ 当前无法获取自动字幕，但可以使用弹幕字幕")

if __name__ == "__main__":
    main()
